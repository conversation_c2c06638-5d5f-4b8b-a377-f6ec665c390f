# 🏫 <PERSON>ệ thống Quản lý Lớp học

Hệ thống quản lý lớp học hoàn chỉnh được phát triển bằng PHP và MySQL, hỗ trợ đầy đủ các tính năng quản lý họ<PERSON>, <PERSON><PERSON><PERSON>, đ<PERSON><PERSON><PERSON>nh, thu chi và tài sản.

## ✨ Tính năng chính

### 🔐 Phân quyền người dùng
- **Admin**: Quản lý toàn hệ thống
- **Quản sinh**: <PERSON><PERSON><PERSON><PERSON> lý học sinh, l<PERSON><PERSON> họ<PERSON>, lị<PERSON> d<PERSON>, tà<PERSON> sản
- **Giáo viên**: <PERSON><PERSON><PERSON><PERSON>nh, xem lịch dạy

### 👥 Quản lý học sinh
- ✅ Thêm/sửa/xóa học sinh với mã tự động
- ✅ Thông tin chi tiết: tê<PERSON>, ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, SĐT
- ✅ <PERSON><PERSON> họ<PERSON> sinh vào lớp
- ✅ Import từ Excel
- ✅ Link Google Drive cho hồ sơ

### 🏫 Quản lý lớp học
- ✅ Tạo/quản lý lớp học
- ✅ Gán học sinh và giáo viên
- ✅ Xem danh sách học sinh trong lớp

### 🗓️ Quản lý lịch dạy
- ✅ 3 khung giờ: Sáng/Trưa/Chiều
- ✅ Kiểm tra trùng lịch tự động
- ✅ Lịch theo ngày và tuần
- ✅ Import/Export Excel

### 📋 Điểm danh học sinh
- ✅ 4 trạng thái: Có mặt/Vắng/Trễ/Có phép
- ✅ Ghi lý do nghỉ
- ✅ Thống kê vắng học
- ✅ Xuất báo cáo PDF/Excel

### 💰 Quản lý thu chi
- ✅ Theo dõi thu/chi theo danh mục
- ✅ Thống kê theo tháng
- ✅ Xuất báo cáo tài chính

### 📦 Quản lý tài sản
- ✅ Thêm tài sản với mã tự động
- ✅ Gán cho lớp/giáo viên
- ✅ Theo dõi tình trạng

### 📢 Hệ thống thông báo
- ✅ Gửi đến toàn bộ hoặc nhóm cụ thể
- ✅ Thông báo khẩn cấp
- ✅ Hiển thị real-time

## 🛠️ Cài đặt

### Yêu cầu hệ thống
- PHP 7.4 trở lên
- MySQL 5.7 trở lên
- Apache/Nginx web server
- Extension: PDO, PDO_MySQL, mbstring

### Bước 1: Tải mã nguồn
```bash
git clone [repository-url]
cd classroom-management
```

### Bước 2: Cấu hình database
1. Tạo database MySQL:
```sql
CREATE DATABASE classroom_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. Import schema:
```bash
mysql -u root -p classroom_management < database/schema.sql
```

3. Cấu hình kết nối trong `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'classroom_management';
private $username = 'root';
private $password = 'your_password';
```

### Bước 3: Cấu hình web server
#### Apache
```apache
<VirtualHost *:80>
    DocumentRoot /path/to/classroom-management
    ServerName classroom.local
    
    <Directory /path/to/classroom-management>
        AllowOverride All
        Require all granted
    </Directory>
</VirtualHost>
```

#### Nginx
```nginx
server {
    listen 80;
    server_name classroom.local;
    root /path/to/classroom-management;
    index index.php;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        include fastcgi_params;
    }
}
```

### Bước 4: Phân quyền thư mục
```bash
chmod 755 classroom-management/
chmod 777 classroom-management/assets/uploads/
```

## 🚀 Sử dụng

### Đăng nhập lần đầu
- URL: `http://your-domain/classroom-management/`
- Tài khoản mặc định:
  - Username: `admin`
  - Password: `admin123`

### Thiết lập ban đầu
1. **Đổi mật khẩu admin**
2. **Tạo tài khoản giáo viên và quản sinh**
3. **Thêm lớp học**
4. **Thêm học sinh**
5. **Lên lịch dạy**

## 📱 Giao diện

### Desktop
- Dashboard tổng quan với thống kê
- Menu điều hướng đầy đủ
- Bảng dữ liệu với tìm kiếm/lọc
- Form nhập liệu thân thiện

### Mobile
- Responsive design
- Menu thu gọn
- Điểm danh dễ dàng trên điện thoại
- Xem lịch học nhanh chóng

## 🔧 Tính năng nâng cao

### Import/Export Excel
- Import danh sách học sinh từ Excel
- Export báo cáo điểm danh
- Export báo cáo thu chi

### Thống kê và báo cáo
- Biểu đồ thống kê vắng học
- Báo cáo tài chính theo tháng
- Thống kê hiệu suất lớp học

### Bảo mật
- Phân quyền chi tiết theo role
- Ghi log hoạt động
- Session timeout
- CSRF protection

### API
- RESTful API cho mobile app
- Webhook cho thông báo
- Integration với Google Drive

## 🗂️ Cấu trúc thư mục

```
classroom-management/
├── config/              # Cấu hình hệ thống
├── includes/            # File include chung
├── modules/             # Các module chức năng
│   ├── students/        # Quản lý học sinh
│   ├── classes/         # Quản lý lớp học
│   ├── schedule/        # Quản lý lịch học
│   ├── attendance/      # Điểm danh
│   ├── finance/         # Thu chi
│   ├── assets/          # Tài sản
│   ├── notifications/   # Thông báo
│   └── users/           # Quản lý người dùng
├── assets/              # CSS, JS, uploads
├── database/            # Schema và migrations
└── api/                 # API endpoints
```

## 🔄 Workflow chuẩn

1. **Admin** tạo tài khoản giáo viên & quản sinh
2. **Admin/Quản sinh** thêm học sinh → gán vào lớp
3. **Quản sinh** lên lịch học → hiển thị trên calendar
4. **Giáo viên** điểm danh trong giờ dạy
5. **Admin/Quản sinh** xem thống kê và báo cáo

## 🛡️ Bảo mật

### Mật khẩu
- Hash bằng bcrypt
- Yêu cầu độ mạnh tối thiểu
- Reset password cho admin

### Session
- Timeout tự động
- Regenerate session ID
- Secure cookie settings

### Database
- Prepared statements
- Input validation
- SQL injection protection

### File Upload
- Kiểm tra extension
- Giới hạn kích thước
- Scan virus (tùy chọn)

## 📞 Hỗ trợ

### Báo lỗi
- Tạo issue trên GitHub
- Mô tả chi tiết lỗi
- Attach log files

### Yêu cầu tính năng
- Tạo feature request
- Mô tả use case
- Đánh giá độ ưu tiên

### Đóng góp
- Fork repository
- Tạo feature branch
- Submit pull request

## 📄 License

MIT License - Xem file LICENSE để biết chi tiết

## 👨‍💻 Phát triển bởi

**Augment Code** - Hệ thống AI hỗ trợ phát triển phần mềm

---

🎯 **Mục tiêu**: Tạo ra hệ thống quản lý lớp học hiện đại, dễ sử dụng và đầy đủ tính năng cho các trung tâm giáo dục.

📧 **Liên hệ**: <EMAIL>
