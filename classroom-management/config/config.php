<?php
/**
 * <PERSON><PERSON><PERSON> hình chung của hệ thống
 */

// Bắt đầu session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Cấu hình múi giờ
date_default_timezone_set('Asia/Ho_Chi_Minh');

// C<PERSON>u hình hiển thị lỗi (tắt trong production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Cấu hình <PERSON>ng dụng
define('APP_NAME', 'H<PERSON> thống Quản lý Lớ<PERSON> học');
define('APP_VERSION', '1.0.0');
define('BASE_URL', 'http://localhost/classroom-management/');

// Cấu hình đường dẫn
define('ROOT_PATH', dirname(__DIR__));
define('UPLOAD_PATH', ROOT_PATH . '/assets/uploads/');
define('ASSETS_PATH', '/classroom-management/assets/');

// <PERSON><PERSON><PERSON> hình bảo mật
define('HASH_ALGO', PASSWORD_DEFAULT);
define('SESSION_TIMEOUT', 3600); // 1 giờ

// Cấu hình phân trang
define('RECORDS_PER_PAGE', 20);

// Cấu hình upload file
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_EXTENSIONS', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx']);

// Cấu hình email (nếu cần)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');

// Roles và permissions
define('ROLES', [
    'admin' => 'Quản trị viên',
    'manager' => 'Quản sinh',
    'teacher' => 'Giáo viên'
]);

define('PERMISSIONS', [
    'admin' => [
        'users_manage', 'students_manage', 'classes_manage', 
        'schedule_manage', 'attendance_view', 'finance_manage', 
        'assets_manage', 'notifications_send', 'reports_view'
    ],
    'manager' => [
        'students_view', 'classes_manage', 'schedule_manage', 
        'attendance_view', 'assets_manage', 'notifications_send', 'reports_view'
    ],
    'teacher' => [
        'attendance_mark', 'schedule_view', 'profile_edit'
    ]
]);

// Khung giờ học
define('TIME_SLOTS', [
    'morning' => [
        'name' => 'Buổi sáng',
        'start' => '07:00',
        'end' => '11:30'
    ],
    'afternoon' => [
        'name' => 'Buổi trưa', 
        'start' => '13:00',
        'end' => '17:00'
    ],
    'evening' => [
        'name' => 'Buổi chiều',
        'start' => '18:00', 
        'end' => '21:00'
    ]
]);

// Trạng thái điểm danh
define('ATTENDANCE_STATUS', [
    'present' => 'Có mặt',
    'absent' => 'Vắng',
    'late' => 'Trễ',
    'excused' => 'Có phép'
]);

// Loại thông báo
define('NOTIFICATION_TYPES', [
    'general' => 'Thông báo chung',
    'schedule_change' => 'Thay đổi lịch học',
    'holiday' => 'Nghỉ lễ',
    'urgent' => 'Khẩn cấp',
    'event' => 'Sự kiện'
]);

// Hàm helper
function formatDate($date, $format = 'd/m/Y') {
    return date($format, strtotime($date));
}

function formatDateTime($datetime, $format = 'd/m/Y H:i') {
    return date($format, strtotime($datetime));
}

function formatCurrency($amount) {
    return number_format($amount, 0, ',', '.') . ' VNĐ';
}

function generateCode($prefix, $length = 6) {
    $number = str_pad(mt_rand(1, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    return $prefix . $number;
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function redirectTo($url) {
    header("Location: " . $url);
    exit();
}

function showAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

function getAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

// Kiểm tra quyền truy cập
function hasPermission($permission) {
    if (!isset($_SESSION['user_role'])) {
        return false;
    }
    
    $userRole = $_SESSION['user_role'];
    return in_array($permission, PERMISSIONS[$userRole] ?? []);
}

// Kiểm tra đăng nhập
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

// Kiểm tra session timeout
function checkSessionTimeout() {
    if (isset($_SESSION['last_activity'])) {
        if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
            session_destroy();
            redirectTo('login.php?timeout=1');
        }
    }
    $_SESSION['last_activity'] = time();
}

// Auto-load classes
spl_autoload_register(function ($class_name) {
    $file = ROOT_PATH . '/includes/' . $class_name . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});
?>
