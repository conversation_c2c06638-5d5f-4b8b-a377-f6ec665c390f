<?php
/**
 * Database Configuration
 * Cấu hình kết nối cơ sở dữ liệu
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'classroom_management';
    private $username = 'root';
    private $password = 'ServBay.dev';
    private $charset = 'utf8mb4';
    public $conn;

    /**
     * Kết nối cơ sở dữ liệu
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }

    /**
     * Thực hiện truy vấn SELECT
     */
    public function select($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch(PDOException $e) {
            error_log("Database Select Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Thực hiện truy vấn INSERT, UPDATE, DELETE
     */
    public function execute($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            return $stmt->execute($params);
        } catch(PDOException $e) {
            error_log("Database Execute Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Lấy ID của bản ghi vừa insert
     */
    public function lastInsertId() {
        return $this->conn->lastInsertId();
    }

    /**
     * Bắt đầu transaction
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->conn->rollback();
    }

    /**
     * Đếm số bản ghi
     */
    public function count($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchColumn();
        } catch(PDOException $e) {
            error_log("Database Count Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Lấy một bản ghi duy nhất
     */
    public function selectOne($query, $params = []) {
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute($params);
            return $stmt->fetch();
        } catch(PDOException $e) {
            error_log("Database SelectOne Error: " . $e->getMessage());
            return false;
        }
    }
}

// Tạo instance global
$database = new Database();
$db = $database->getConnection();

// Kiểm tra kết nối
if (!$db) {
    die("Không thể kết nối đến cơ sở dữ liệu!");
}
?>
