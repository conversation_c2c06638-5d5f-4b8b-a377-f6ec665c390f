<?php
/**
 * Trang Dashboard chính
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/includes/auth.php';
require_once __DIR__ . '/includes/functions.php';

$auth = new Auth();
$auth->requireLogin();

$pageTitle = 'Dashboard';
$breadcrumb = generateBreadcrumb([
    ['title' => 'Dashboard', 'url' => '#']
]);

// Lấy thống kê tổng quan
try {
    // Tổng số học sinh
    $totalStudents = $database->count("SELECT COUNT(*) FROM students WHERE status = 'active'");
    
    // Tổng số lớp học
    $totalClasses = $database->count("SELECT COUNT(*) FROM classes WHERE status = 'active'");
    
    // Tổng số giáo viên
    $totalTeachers = $database->count("SELECT COUNT(*) FROM users WHERE role = 'teacher' AND status = 'active'");
    
    // Lịch học hôm nay
    $todaySchedules = $database->count("SELECT COUNT(*) FROM schedules WHERE schedule_date = CURDATE() AND status = 'scheduled'");
    
    // Học sinh vắng hôm nay
    $todayAbsent = $database->count("
        SELECT COUNT(DISTINCT a.student_id) 
        FROM attendance a 
        JOIN schedules s ON a.schedule_id = s.id 
        WHERE s.schedule_date = CURDATE() AND a.status = 'absent'
    ");
    
    // Thu chi tháng này
    $currentMonth = date('Y-m');
    $monthlyIncome = $database->selectOne("
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM finance 
        WHERE type = 'income' AND DATE_FORMAT(transaction_date, '%Y-%m') = ?
    ", [$currentMonth])['total'] ?? 0;
    
    $monthlyExpense = $database->selectOne("
        SELECT COALESCE(SUM(amount), 0) as total 
        FROM finance 
        WHERE type = 'expense' AND DATE_FORMAT(transaction_date, '%Y-%m') = ?
    ", [$currentMonth])['total'] ?? 0;
    
    // Lịch học tuần này
    $weekStart = date('Y-m-d', strtotime('monday this week'));
    $weekEnd = date('Y-m-d', strtotime('sunday this week'));
    
    $weekSchedules = $database->select("
        SELECT s.*, c.class_name, u.full_name as teacher_name,
               DATE_FORMAT(s.schedule_date, '%d/%m') as date_formatted,
               CASE s.time_slot
                   WHEN 'morning' THEN 'Sáng'
                   WHEN 'afternoon' THEN 'Trưa' 
                   WHEN 'evening' THEN 'Chiều'
               END as time_slot_name
        FROM schedules s
        JOIN classes c ON s.class_id = c.id
        JOIN users u ON s.teacher_id = u.id
        WHERE s.schedule_date BETWEEN ? AND ?
        AND s.status = 'scheduled'
        ORDER BY s.schedule_date, s.time_slot
    ", [$weekStart, $weekEnd]);
    
    // Thông báo mới nhất
    $recentNotifications = $database->select("
        SELECT n.*, u.full_name as created_by_name,
               CASE n.target_audience
                   WHEN 'all' THEN 'Tất cả'
                   WHEN 'teachers' THEN 'Giáo viên'
                   WHEN 'managers' THEN 'Quản sinh'
                   WHEN 'specific_class' THEN CONCAT('Lớp ', c.class_name)
               END as target_name
        FROM notifications n
        LEFT JOIN users u ON n.created_by = u.id
        LEFT JOIN classes c ON n.target_class_id = c.id
        WHERE n.expires_at IS NULL OR n.expires_at > NOW()
        ORDER BY n.created_at DESC
        LIMIT 5
    ");
    
} catch (Exception $e) {
    error_log("Dashboard Error: " . $e->getMessage());
    showAlert('Có lỗi xảy ra khi tải dữ liệu dashboard!', 'error');
}

include 'includes/header.php';
?>

<div class="row">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card bg-primary">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h3><?php echo number_format($totalStudents); ?></h3>
                    <p><i class="fas fa-user-graduate"></i> Học sinh</p>
                </div>
                <div class="ms-3">
                    <i class="fas fa-user-graduate fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card bg-success">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h3><?php echo number_format($totalClasses); ?></h3>
                    <p><i class="fas fa-chalkboard"></i> Lớp học</p>
                </div>
                <div class="ms-3">
                    <i class="fas fa-chalkboard fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card bg-warning">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h3><?php echo number_format($totalTeachers); ?></h3>
                    <p><i class="fas fa-chalkboard-teacher"></i> Giáo viên</p>
                </div>
                <div class="ms-3">
                    <i class="fas fa-chalkboard-teacher fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="dashboard-card bg-info">
            <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                    <h3><?php echo number_format($todaySchedules); ?></h3>
                    <p><i class="fas fa-calendar-day"></i> Lịch hôm nay</p>
                </div>
                <div class="ms-3">
                    <i class="fas fa-calendar-day fa-3x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Stats -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> Thống kê nhanh
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="stat-card">
                            <div class="stat-icon text-danger">
                                <i class="fas fa-user-times"></i>
                            </div>
                            <div class="stat-number text-danger"><?php echo $todayAbsent; ?></div>
                            <div class="stat-label">Vắng hôm nay</div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 text-center">
                        <div class="stat-card">
                            <div class="stat-icon text-success">
                                <i class="fas fa-arrow-up"></i>
                            </div>
                            <div class="stat-number text-success"><?php echo formatCurrency($monthlyIncome); ?></div>
                            <div class="stat-label">Thu tháng này</div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 text-center">
                        <div class="stat-card">
                            <div class="stat-icon text-warning">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="stat-number text-warning"><?php echo formatCurrency($monthlyExpense); ?></div>
                            <div class="stat-label">Chi tháng này</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Weekly Schedule -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-calendar-week"></i> Lịch học tuần này</span>
                <a href="modules/schedule/calendar.php" class="btn btn-sm btn-primary">
                    <i class="fas fa-calendar"></i> Xem lịch đầy đủ
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($weekSchedules)): ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <p>Không có lịch học nào trong tuần này</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Ngày</th>
                                <th>Khung giờ</th>
                                <th>Lớp</th>
                                <th>Giáo viên</th>
                                <th>Môn học</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($weekSchedules as $schedule): ?>
                            <tr>
                                <td>
                                    <strong><?php echo $schedule['date_formatted']; ?></strong>
                                </td>
                                <td>
                                    <span class="badge bg-info"><?php echo $schedule['time_slot_name']; ?></span>
                                </td>
                                <td><?php echo htmlspecialchars($schedule['class_name']); ?></td>
                                <td><?php echo htmlspecialchars($schedule['teacher_name']); ?></td>
                                <td><?php echo htmlspecialchars($schedule['subject'] ?? 'Chưa xác định'); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Notifications & Quick Actions -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-bolt"></i> Thao tác nhanh
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <?php if (hasPermission('students_manage')): ?>
                    <a href="modules/students/add.php" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Thêm học sinh
                    </a>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('schedule_manage')): ?>
                    <a href="modules/schedule/add.php" class="btn btn-success">
                        <i class="fas fa-calendar-plus"></i> Tạo lịch học
                    </a>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('attendance_mark')): ?>
                    <a href="modules/attendance/index.php" class="btn btn-warning">
                        <i class="fas fa-clipboard-check"></i> Điểm danh
                    </a>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('notifications_send')): ?>
                    <a href="modules/notifications/add.php" class="btn btn-info">
                        <i class="fas fa-bullhorn"></i> Gửi thông báo
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Recent Notifications -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-bell"></i> Thông báo mới</span>
                <a href="modules/notifications/index.php" class="btn btn-sm btn-outline-primary">
                    Xem tất cả
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($recentNotifications)): ?>
                <div class="text-center text-muted py-3">
                    <i class="fas fa-bell-slash fa-2x mb-2"></i>
                    <p>Không có thông báo mới</p>
                </div>
                <?php else: ?>
                <?php foreach ($recentNotifications as $notification): ?>
                <div class="notification-item <?php echo $notification['is_urgent'] ? 'urgent' : ''; ?>">
                    <div class="notification-header">
                        <span class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></span>
                        <span class="notification-time"><?php echo formatDateTime($notification['created_at']); ?></span>
                    </div>
                    <div class="notification-content">
                        <?php echo nl2br(htmlspecialchars(substr($notification['content'], 0, 100))); ?>
                        <?php if (strlen($notification['content']) > 100): ?>...<?php endif; ?>
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-user"></i> <?php echo htmlspecialchars($notification['created_by_name']); ?>
                        • <i class="fas fa-users"></i> <?php echo $notification['target_name']; ?>
                    </small>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
