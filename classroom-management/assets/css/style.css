/* Custom CSS cho hệ thống quản lý lớp học */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.dashboard-card.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.dashboard-card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.dashboard-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
}

.dashboard-card.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.dashboard-card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.dashboard-card p {
    font-size: 1.1rem;
    margin-bottom: 0;
    opacity: 0.9;
}

/* Tables */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border: none;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h2 {
    color: #333;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
    margin-bottom: 0;
}

/* Calendar */
.calendar-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #dee2e6;
    border-radius: 10px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 1rem;
    min-height: 120px;
    position: relative;
    transition: background-color 0.2s;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-day-header {
    background: #667eea;
    color: white;
    padding: 0.5rem;
    text-align: center;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
}

.calendar-day-number {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.calendar-event {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
    cursor: pointer;
}

.calendar-event:hover {
    background: #0056b3;
}

/* Attendance */
.attendance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.student-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.student-card:hover {
    transform: translateY(-2px);
}

.student-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.student-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 1rem;
}

.attendance-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.attendance-btn {
    flex: 1;
    min-width: 60px;
    padding: 0.5rem;
    border: none;
    border-radius: 5px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.attendance-btn.present {
    background: #28a745;
    color: white;
}

.attendance-btn.absent {
    background: #dc3545;
    color: white;
}

.attendance-btn.late {
    background: #ffc107;
    color: #212529;
}

.attendance-btn.excused {
    background: #17a2b8;
    color: white;
}

.attendance-btn:hover {
    opacity: 0.8;
    transform: scale(1.05);
}

/* Statistics */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 1.1rem;
}

/* Notifications */
.notification-item {
    background: white;
    border-left: 4px solid #007bff;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 10px 10px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.notification-item:hover {
    transform: translateX(5px);
}

.notification-item.urgent {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-weight: bold;
    color: #333;
}

.notification-time {
    color: #666;
    font-size: 0.85rem;
}

.notification-content {
    color: #555;
    line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
    .dashboard-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .dashboard-card h3 {
        font-size: 2rem;
    }
    
    .calendar-day {
        min-height: 80px;
        padding: 0.5rem;
    }
    
    .attendance-grid {
        grid-template-columns: 1fr;
    }
    
    .login-card {
        margin: 1rem;
        padding: 2rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Loading spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
