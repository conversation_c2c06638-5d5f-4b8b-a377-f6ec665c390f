/* Custom CSS cho hệ thống quản lý lớp học - <PERSON> & White Theme */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 50%, #ffd4b3 100%);
    min-height: 100vh;
    animation: backgroundShift 10s ease-in-out infinite alternate;
}

@keyframes backgroundShift {
    0% { background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 50%, #ffd4b3 100%); }
    100% { background: linear-gradient(135deg, #ffd4b3 0%, #ffe8d6 50%, #fff5f0 100%); }
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    animation: brandGlow 2s ease-in-out infinite alternate;
}

@keyframes brandGlow {
    0% { text-shadow: 0 0 5px rgba(255, 165, 0, 0.5); }
    100% { text-shadow: 0 0 20px rgba(255, 165, 0, 0.8), 0 0 30px rgba(255, 165, 0, 0.6); }
}

/* Navbar Styling */
.navbar {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 50%, #f7931e 100%) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(255, 140, 66, 0.3);
    transition: all 0.3s ease;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: white;
    transition: width 0.3s ease;
}

.navbar-nav .nav-link:hover::before {
    width: 100%;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    transform: translateY(-2px);
}

.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(255, 140, 66, 0.2);
    animation: dropdownSlide 0.3s ease-out;
}

@keyframes dropdownSlide {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    transition: all 0.3s ease;
    border-radius: 10px;
    margin: 2px 5px;
}

.dropdown-item:hover {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 100%);
    color: white;
    transform: translateX(5px);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(255, 165, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: cardFloat 6s ease-in-out infinite;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(255, 165, 0, 0.2);
    background: rgba(255, 255, 255, 1);
}

@keyframes cardFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-3px); }
}

.card-header {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 50%, #f7931e 100%);
    color: white;
    border-radius: 15px 15px 0 0 !important;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Dashboard Cards */
.dashboard-card {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 50%, #f7931e 100%);
    color: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    animation: dashboardPulse 4s ease-in-out infinite;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.dashboard-card:hover::before {
    transform: translateX(100%);
}

.dashboard-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(255, 140, 66, 0.3);
}

@keyframes dashboardPulse {
    0%, 100% { box-shadow: 0 8px 32px rgba(255, 140, 66, 0.2); }
    50% { box-shadow: 0 8px 32px rgba(255, 140, 66, 0.4); }
}

.dashboard-card.bg-primary {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 100%);
    animation: primaryGlow 3s ease-in-out infinite alternate;
}

.dashboard-card.bg-success {
    background: linear-gradient(135deg, #ff9f43 0%, #ff7f50 100%);
    animation: successGlow 3s ease-in-out infinite alternate;
}

.dashboard-card.bg-warning {
    background: linear-gradient(135deg, #ffb347 0%, #ffa500 100%);
    animation: warningGlow 3s ease-in-out infinite alternate;
}

.dashboard-card.bg-danger {
    background: linear-gradient(135deg, #ff7f7f 0%, #ff6347 100%);
    animation: dangerGlow 3s ease-in-out infinite alternate;
}

.dashboard-card.bg-info {
    background: linear-gradient(135deg, #87ceeb 0%, #4682b4 100%);
    animation: infoGlow 3s ease-in-out infinite alternate;
}

@keyframes primaryGlow {
    0% { box-shadow: 0 8px 32px rgba(255, 140, 66, 0.3); }
    100% { box-shadow: 0 8px 32px rgba(255, 140, 66, 0.6); }
}

@keyframes successGlow {
    0% { box-shadow: 0 8px 32px rgba(255, 159, 67, 0.3); }
    100% { box-shadow: 0 8px 32px rgba(255, 159, 67, 0.6); }
}

@keyframes warningGlow {
    0% { box-shadow: 0 8px 32px rgba(255, 179, 71, 0.3); }
    100% { box-shadow: 0 8px 32px rgba(255, 179, 71, 0.6); }
}

@keyframes dangerGlow {
    0% { box-shadow: 0 8px 32px rgba(255, 127, 127, 0.3); }
    100% { box-shadow: 0 8px 32px rgba(255, 127, 127, 0.6); }
}

@keyframes infoGlow {
    0% { box-shadow: 0 8px 32px rgba(135, 206, 235, 0.3); }
    100% { box-shadow: 0 8px 32px rgba(135, 206, 235, 0.6); }
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.dashboard-card p {
    font-size: 1.1rem;
    margin-bottom: 0;
    opacity: 0.9;
}

/* Tables */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Buttons */
.btn {
    border-radius: 30px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 25px rgba(255, 140, 66, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 140, 66, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #ff9f43 0%, #ff7f50 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 159, 67, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, #ffb347 0%, #ffa500 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 179, 71, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #ff7f7f 0%, #ff6347 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(255, 127, 127, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, #87ceeb 0%, #4682b4 100%);
    border: none;
    box-shadow: 0 4px 15px rgba(135, 206, 235, 0.3);
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* Login Page */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 50%, #f7931e 100%);
    animation: loginBackground 8s ease-in-out infinite alternate;
}

@keyframes loginBackground {
    0% { background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 50%, #f7931e 100%); }
    100% { background: linear-gradient(135deg, #f7931e 0%, #ff6b35 50%, #ff8c42 100%); }
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 3rem;
    box-shadow: 0 25px 50px rgba(255, 140, 66, 0.2);
    width: 100%;
    max-width: 400px;
    animation: loginCardFloat 6s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

@keyframes loginCardFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 140, 66, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    animation: loginShimmer 4s infinite;
}

@keyframes loginShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header h2 {
    color: #333;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.login-header p {
    color: #666;
    margin-bottom: 0;
}

/* Calendar */
.calendar-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #dee2e6;
    border-radius: 10px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 1rem;
    min-height: 120px;
    position: relative;
    transition: background-color 0.2s;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-day-header {
    background: #667eea;
    color: white;
    padding: 0.5rem;
    text-align: center;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
}

.calendar-day-number {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.calendar-event {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
    cursor: pointer;
}

.calendar-event:hover {
    background: #0056b3;
}

/* Attendance */
.attendance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.student-card {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.student-card:hover {
    transform: translateY(-2px);
}

.student-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.student-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 1rem;
}

.attendance-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.attendance-btn {
    flex: 1;
    min-width: 60px;
    padding: 0.5rem;
    border: none;
    border-radius: 5px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.attendance-btn.present {
    background: #28a745;
    color: white;
}

.attendance-btn.absent {
    background: #dc3545;
    color: white;
}

.attendance-btn.late {
    background: #ffc107;
    color: #212529;
}

.attendance-btn.excused {
    background: #17a2b8;
    color: white;
}

.attendance-btn:hover {
    opacity: 0.8;
    transform: scale(1.05);
}

/* Statistics */
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-size: 1.1rem;
}

/* Notifications */
.notification-item {
    background: white;
    border-left: 4px solid #007bff;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 10px 10px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.notification-item:hover {
    transform: translateX(5px);
}

.notification-item.urgent {
    border-left-color: #dc3545;
    background: #fff5f5;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.notification-title {
    font-weight: bold;
    color: #333;
}

.notification-time {
    color: #666;
    font-size: 0.85rem;
}

.notification-content {
    color: #555;
    line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
    .dashboard-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .dashboard-card h3 {
        font-size: 2rem;
    }
    
    .calendar-day {
        min-height: 80px;
        padding: 0.5rem;
    }
    
    .attendance-grid {
        grid-template-columns: 1fr;
    }
    
    .login-card {
        margin: 1rem;
        padding: 2rem;
    }
}

/* Advanced Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-200deg);
    }
    to {
        opacity: 1;
        transform: rotate(0deg);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.fade-in {
    animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.bounce-in {
    animation: bounceIn 0.8s ease-out;
}

.rotate-in {
    animation: rotateIn 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Loading spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 100%);
    border-radius: 10px;
    transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    transform: scale(1.1);
}

/* Page Loading Animation */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #ff8c42 0%, #ff6b35 50%, #f7931e 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeOut 1s ease-out 2s forwards;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes fadeOut {
    to {
        opacity: 0;
        visibility: hidden;
    }
}

/* Floating Elements */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Glowing Text */
.glow-text {
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from {
        text-shadow: 0 0 5px rgba(255, 140, 66, 0.5);
    }
    to {
        text-shadow: 0 0 20px rgba(255, 140, 66, 0.8), 0 0 30px rgba(255, 140, 66, 0.6);
    }
}

/* Particle Background */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 140, 66, 0.6);
    border-radius: 50%;
    animation: particleFloat 6s infinite linear;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-10vh) rotate(360deg);
        opacity: 0;
    }
}

/* Hover Effects */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 140, 66, 0.2);
}

/* Success Animation */
.success-checkmark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    stroke-width: 2;
    stroke: #4bb71b;
    stroke-miterlimit: 10;
    box-shadow: inset 0px 0px 0px #4bb71b;
    animation: fill 0.4s ease-in-out 0.4s forwards, scale 0.3s ease-in-out 0.9s both;
    position: relative;
    top: 5px;
    right: 5px;
    margin: 0 auto;
}

@keyframes scale {
    0%, 100% {
        transform: none;
    }
    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 30px #4bb71b;
    }
}
