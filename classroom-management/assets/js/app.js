/**
 * Main JavaScript file for Classroom Management System
 */

// Global variables
let currentUser = null;
let notifications = [];

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize application
 */
function initializeApp() {
    // Initialize Bootstrap components
    initializeBootstrapComponents();
    
    // Initialize form validations
    initializeFormValidations();
    
    // Initialize data tables
    initializeDataTables();
    
    // Initialize notifications
    initializeNotifications();
    
    // Initialize auto-save functionality
    initializeAutoSave();
    
    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
    
    console.log('Classroom Management System initialized successfully');
}

/**
 * Initialize Bootstrap components
 */
function initializeBootstrapComponents() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Initialize modals
    var modalList = [].slice.call(document.querySelectorAll('.modal'));
    modalList.forEach(function(modal) {
        new bootstrap.Modal(modal);
    });
}

/**
 * Initialize form validations
 */
function initializeFormValidations() {
    // Bootstrap form validation
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // Custom validations
    initializeCustomValidations();
}

/**
 * Initialize custom validations
 */
function initializeCustomValidations() {
    // Email validation
    const emailInputs = document.querySelectorAll('input[type="email"]');
    emailInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            validateEmailField(this);
        });
    });
    
    // Phone validation
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(function(input) {
        input.addEventListener('blur', function() {
            validatePhoneField(this);
        });
    });
    
    // Date validation
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(function(input) {
        input.addEventListener('change', function() {
            validateDateField(this);
        });
    });
}

/**
 * Validate email field
 */
function validateEmailField(input) {
    const email = input.value.trim();
    const isValid = validateEmail(email);
    
    if (email && !isValid) {
        input.classList.add('is-invalid');
        showFieldError(input, 'Email không hợp lệ');
    } else {
        input.classList.remove('is-invalid');
        hideFieldError(input);
    }
}

/**
 * Validate phone field
 */
function validatePhoneField(input) {
    const phone = input.value.trim();
    const isValid = validatePhone(phone);
    
    if (phone && !isValid) {
        input.classList.add('is-invalid');
        showFieldError(input, 'Số điện thoại không hợp lệ');
    } else {
        input.classList.remove('is-invalid');
        hideFieldError(input);
    }
}

/**
 * Validate date field
 */
function validateDateField(input) {
    const date = new Date(input.value);
    const today = new Date();
    
    if (input.hasAttribute('data-min-age')) {
        const minAge = parseInt(input.getAttribute('data-min-age'));
        const minDate = new Date();
        minDate.setFullYear(today.getFullYear() - minAge);
        
        if (date > minDate) {
            input.classList.add('is-invalid');
            showFieldError(input, `Tuổi tối thiểu là ${minAge}`);
            return;
        }
    }
    
    if (input.hasAttribute('data-max-age')) {
        const maxAge = parseInt(input.getAttribute('data-max-age'));
        const maxDate = new Date();
        maxDate.setFullYear(today.getFullYear() - maxAge);
        
        if (date < maxDate) {
            input.classList.add('is-invalid');
            showFieldError(input, `Tuổi tối đa là ${maxAge}`);
            return;
        }
    }
    
    input.classList.remove('is-invalid');
    hideFieldError(input);
}

/**
 * Show field error
 */
function showFieldError(input, message) {
    let errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        input.parentNode.appendChild(errorDiv);
    }
    errorDiv.textContent = message;
}

/**
 * Hide field error
 */
function hideFieldError(input) {
    const errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Initialize data tables
 */
function initializeDataTables() {
    // Add search functionality to tables
    const searchInputs = document.querySelectorAll('.table-search');
    searchInputs.forEach(function(input) {
        const tableId = input.getAttribute('data-table');
        if (tableId) {
            initializeTableSearch(input, tableId);
        }
    });
    
    // Add sorting functionality
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(function(header) {
        header.addEventListener('click', function() {
            sortTable(this);
        });
    });
}

/**
 * Initialize table search
 */
function initializeTableSearch(input, tableId) {
    input.addEventListener('keyup', function() {
        const filter = this.value.toLowerCase();
        const table = document.getElementById(tableId);
        const rows = table.getElementsByTagName('tr');
        
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.getElementsByTagName('td');
            let found = false;
            
            for (let j = 0; j < cells.length; j++) {
                if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
            
            row.style.display = found ? '' : 'none';
        }
    });
}

/**
 * Sort table
 */
function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = !header.classList.contains('sort-asc');
    
    // Remove existing sort classes
    header.parentNode.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // Add new sort class
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    
    // Sort rows
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        // Try to parse as numbers
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        // Compare as strings
        return isAscending ? aText.localeCompare(bText) : bText.localeCompare(aText);
    });
    
    // Reorder rows in DOM
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * Initialize notifications
 */
function initializeNotifications() {
    // Load initial notifications
    loadNotifications();
    
    // Set up periodic refresh
    setInterval(loadNotifications, 30000); // Every 30 seconds
    
    // Mark notifications as read when clicked
    document.addEventListener('click', function(e) {
        if (e.target.closest('.notification-item')) {
            const notificationId = e.target.closest('.notification-item').getAttribute('data-id');
            if (notificationId) {
                markNotificationAsRead(notificationId);
            }
        }
    });
}

/**
 * Load notifications
 */
function loadNotifications() {
    fetch('../../api/notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                notifications = data.notifications;
                updateNotificationUI();
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
        });
}

/**
 * Update notification UI
 */
function updateNotificationUI() {
    const badge = document.getElementById('notification-count');
    const dropdown = document.getElementById('notification-dropdown');
    
    if (badge) {
        const unreadCount = notifications.filter(n => !n.is_read).length;
        badge.textContent = unreadCount;
        badge.style.display = unreadCount > 0 ? 'inline' : 'none';
    }
    
    if (dropdown) {
        updateNotificationDropdown(dropdown);
    }
}

/**
 * Update notification dropdown
 */
function updateNotificationDropdown(dropdown) {
    let html = '<li><h6 class="dropdown-header">Thông báo mới</h6></li>';
    html += '<li><hr class="dropdown-divider"></li>';
    
    if (notifications.length === 0) {
        html += '<li><span class="dropdown-item-text text-muted">Không có thông báo mới</span></li>';
    } else {
        const recentNotifications = notifications.slice(0, 5);
        recentNotifications.forEach(notification => {
            const isUnread = !notification.is_read ? 'fw-bold' : '';
            html += `<li>
                <a class="dropdown-item ${isUnread}" href="#" data-id="${notification.id}">
                    <div class="notification-title">${notification.title}</div>
                    <small class="text-muted">${formatDateTime(notification.created_at)}</small>
                </a>
            </li>`;
        });
    }
    
    html += '<li><hr class="dropdown-divider"></li>';
    html += '<li><a class="dropdown-item text-center" href="../../modules/notifications/index.php">Xem tất cả</a></li>';
    
    dropdown.innerHTML = html;
}

/**
 * Mark notification as read
 */
function markNotificationAsRead(notificationId) {
    fetch('../../api/notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'mark_read',
            notification_id: notificationId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update local notifications array
            const notification = notifications.find(n => n.id == notificationId);
            if (notification) {
                notification.is_read = true;
                updateNotificationUI();
            }
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

/**
 * Initialize auto-save functionality
 */
function initializeAutoSave() {
    const autoSaveForms = document.querySelectorAll('.auto-save');
    autoSaveForms.forEach(function(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(function(input) {
            input.addEventListener('change', function() {
                autoSaveForm(form);
            });
        });
    });
}

/**
 * Auto-save form data
 */
function autoSaveForm(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // Save to localStorage
    const formId = form.id || 'auto-save-form';
    localStorage.setItem(`auto-save-${formId}`, JSON.stringify(data));
    
    // Show auto-save indicator
    showAutoSaveIndicator();
}

/**
 * Show auto-save indicator
 */
function showAutoSaveIndicator() {
    let indicator = document.getElementById('auto-save-indicator');
    if (!indicator) {
        indicator = document.createElement('div');
        indicator.id = 'auto-save-indicator';
        indicator.className = 'position-fixed top-0 end-0 m-3 alert alert-success alert-dismissible fade';
        indicator.innerHTML = '<i class="fas fa-save"></i> Đã tự động lưu';
        document.body.appendChild(indicator);
    }
    
    indicator.classList.add('show');
    
    setTimeout(() => {
        indicator.classList.remove('show');
    }, 2000);
}

/**
 * Initialize keyboard shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+S: Save form
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const form = document.querySelector('form');
            if (form) {
                form.submit();
            }
        }
        
        // Ctrl+N: New record (if add button exists)
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const addButton = document.querySelector('.btn-add, [href*="add.php"]');
            if (addButton) {
                addButton.click();
            }
        }
        
        // Escape: Close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) {
                    modal.hide();
                }
            }
        }
    });
}

/**
 * Utility functions
 */

// Format date time
function formatDateTime(dateTime) {
    const date = new Date(dateTime);
    return date.toLocaleString('vi-VN');
}

// Show loading state
function showLoading(element, text = 'Đang xử lý...') {
    const originalContent = element.innerHTML;
    element.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${text}`;
    element.disabled = true;
    
    return function hideLoading() {
        element.innerHTML = originalContent;
        element.disabled = false;
    };
}

// Show toast notification
function showToast(message, type = 'info') {
    const toastContainer = getOrCreateToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// Get or create toast container
function getOrCreateToastContainer() {
    let container = document.getElementById('toast-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(container);
    }
    return container;
}

// Confirm dialog
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// Export table to Excel (requires SheetJS)
function exportTableToExcel(tableId, filename = 'export') {
    const table = document.getElementById(tableId);
    if (!table) {
        showToast('Không tìm thấy bảng dữ liệu', 'error');
        return;
    }
    
    // Check if SheetJS is available
    if (typeof XLSX === 'undefined') {
        showToast('Thư viện xuất Excel chưa được tải', 'error');
        return;
    }
    
    const wb = XLSX.utils.table_to_book(table);
    XLSX.writeFile(wb, `${filename}.xlsx`);
}

// Print element
function printElement(elementId) {
    const element = document.getElementById(elementId);
    if (!element) {
        showToast('Không tìm thấy nội dung để in', 'error');
        return;
    }
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>In ấn</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    @media print {
                        .no-print { display: none !important; }
                        body { font-size: 12px; }
                        .card { border: 1px solid #000 !important; }
                    }
                </style>
            </head>
            <body>
                <div class="container-fluid">
                    ${element.innerHTML}
                </div>
                <script>
                    window.onload = function() {
                        window.print();
                        window.close();
                    }
                </script>
            </body>
        </html>
    `);
    printWindow.document.close();
}

// Global error handler
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    // You can send error reports to server here
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    // You can send error reports to server here
});
