<?php
/**
 * <PERSON><PERSON><PERSON>n lý lịch học
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = new Auth();
$auth->requirePermission('schedule_view');

$pageTitle = 'Lịch học';
$breadcrumb = generateBreadcrumb([
    ['title' => 'Dashboard', 'url' => '../../index.php'],
    ['title' => 'Lịch học', 'url' => '#']
]);

// Xử lý tìm kiếm và lọc
$dateFilter = sanitizeInput($_GET['date'] ?? date('Y-m-d'));
$classFilter = sanitizeInput($_GET['class'] ?? '');
$teacherFilter = sanitizeInput($_GET['teacher'] ?? '');
$timeSlotFilter = sanitizeInput($_GET['time_slot'] ?? '');

// Xây dựng query
$whereConditions = ['s.schedule_date = ?'];
$params = [$dateFilter];

if (!empty($classFilter)) {
    $whereConditions[] = "s.class_id = ?";
    $params[] = $classFilter;
}

if (!empty($teacherFilter)) {
    $whereConditions[] = "s.teacher_id = ?";
    $params[] = $teacherFilter;
}

if (!empty($timeSlotFilter)) {
    $whereConditions[] = "s.time_slot = ?";
    $params[] = $timeSlotFilter;
}

$whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

// Lấy lịch học
$schedulesQuery = "
    SELECT s.*, c.class_name, c.class_code, u.full_name as teacher_name,
           CASE s.time_slot
               WHEN 'morning' THEN 'Buổi sáng'
               WHEN 'afternoon' THEN 'Buổi trưa'
               WHEN 'evening' THEN 'Buổi chiều'
           END as time_slot_name,
           CASE s.status
               WHEN 'scheduled' THEN 'Đã lên lịch'
               WHEN 'completed' THEN 'Đã hoàn thành'
               WHEN 'cancelled' THEN 'Đã hủy'
           END as status_name
    FROM schedules s
    JOIN classes c ON s.class_id = c.id
    JOIN users u ON s.teacher_id = u.id
    $whereClause
    ORDER BY s.time_slot, c.class_name
";

$schedules = $database->select($schedulesQuery, $params);

// Lấy danh sách lớp và giáo viên cho filter
$classes = $database->select("SELECT id, class_name FROM classes WHERE status = 'active' ORDER BY class_name");
$teachers = $database->select("SELECT id, full_name FROM users WHERE role = 'teacher' AND status = 'active' ORDER BY full_name");

// Xử lý xóa lịch học
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    if (!hasPermission('schedule_manage')) {
        showAlert('Bạn không có quyền xóa lịch học!', 'error');
    } else {
        $scheduleId = intval($_POST['schedule_id']);
        
        try {
            $database->beginTransaction();
            
            // Xóa điểm danh liên quan
            $database->execute("DELETE FROM attendance WHERE schedule_id = ?", [$scheduleId]);
            
            // Xóa lịch học
            $database->execute("DELETE FROM schedules WHERE id = ?", [$scheduleId]);
            
            $database->commit();
            
            $auth->logActivity($_SESSION['user_id'], 'delete', 'schedules', $scheduleId);
            showAlert('Xóa lịch học thành công!', 'success');
            
            // Refresh trang
            redirectTo($_SERVER['REQUEST_URI']);
        } catch (Exception $e) {
            $database->rollback();
            error_log("Delete Schedule Error: " . $e->getMessage());
            showAlert('Có lỗi xảy ra khi xóa lịch học!', 'error');
        }
    }
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt"></i> Lịch học ngày <?php echo formatDate($dateFilter); ?>
                </h5>
                <div>
                    <?php if (hasPermission('schedule_manage')): ?>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tạo lịch học
                    </a>
                    <a href="calendar.php" class="btn btn-success">
                        <i class="fas fa-calendar"></i> Xem lịch tuần
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Filters -->
                <form method="GET" class="row g-3 mb-4">
                    <div class="col-md-3">
                        <label class="form-label">Ngày</label>
                        <input type="date" class="form-control" name="date" 
                               value="<?php echo htmlspecialchars($dateFilter); ?>">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Lớp học</label>
                        <select name="class" class="form-select">
                            <option value="">-- Tất cả lớp --</option>
                            <?php foreach ($classes as $class): ?>
                            <option value="<?php echo $class['id']; ?>" 
                                    <?php echo $classFilter == $class['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($class['class_name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Giáo viên</label>
                        <select name="teacher" class="form-select">
                            <option value="">-- Tất cả giáo viên --</option>
                            <?php foreach ($teachers as $teacher): ?>
                            <option value="<?php echo $teacher['id']; ?>" 
                                    <?php echo $teacherFilter == $teacher['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($teacher['full_name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Khung giờ</label>
                        <select name="time_slot" class="form-select">
                            <option value="">-- Tất cả khung giờ --</option>
                            <option value="morning" <?php echo $timeSlotFilter === 'morning' ? 'selected' : ''; ?>>Buổi sáng</option>
                            <option value="afternoon" <?php echo $timeSlotFilter === 'afternoon' ? 'selected' : ''; ?>>Buổi trưa</option>
                            <option value="evening" <?php echo $timeSlotFilter === 'evening' ? 'selected' : ''; ?>>Buổi chiều</option>
                        </select>
                    </div>
                    
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Tìm kiếm
                        </button>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="fas fa-refresh"></i> Làm mới
                        </a>
                    </div>
                </form>
                
                <!-- Schedule Grid -->
                <?php if (empty($schedules)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Không có lịch học nào trong ngày này</h5>
                    <?php if (hasPermission('schedule_manage')): ?>
                    <a href="add.php?date=<?php echo $dateFilter; ?>" class="btn btn-primary mt-3">
                        <i class="fas fa-plus"></i> Tạo lịch học
                    </a>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <div class="row">
                    <?php 
                    $timeSlots = ['morning' => 'Buổi sáng', 'afternoon' => 'Buổi trưa', 'evening' => 'Buổi chiều'];
                    foreach ($timeSlots as $slot => $slotName):
                        $slotSchedules = array_filter($schedules, function($s) use ($slot) {
                            return $s['time_slot'] === $slot;
                        });
                    ?>
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-clock"></i> <?php echo $slotName; ?>
                                    <span class="badge bg-light text-dark ms-2"><?php echo count($slotSchedules); ?></span>
                                </h6>
                            </div>
                            <div class="card-body">
                                <?php if (empty($slotSchedules)): ?>
                                <div class="text-center text-muted py-3">
                                    <i class="fas fa-calendar-times fa-2x mb-2"></i>
                                    <p>Không có lịch học</p>
                                </div>
                                <?php else: ?>
                                <?php foreach ($slotSchedules as $schedule): ?>
                                <div class="schedule-item mb-3 p-3 border rounded">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="mb-0">
                                            <span class="badge bg-info"><?php echo htmlspecialchars($schedule['class_name']); ?></span>
                                        </h6>
                                        <span class="badge bg-<?php echo $schedule['status'] === 'completed' ? 'success' : ($schedule['status'] === 'cancelled' ? 'danger' : 'warning'); ?>">
                                            <?php echo $schedule['status_name']; ?>
                                        </span>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-chalkboard-teacher"></i> 
                                            <?php echo htmlspecialchars($schedule['teacher_name']); ?>
                                        </small>
                                    </div>
                                    
                                    <?php if ($schedule['subject']): ?>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-book"></i> 
                                            <?php echo htmlspecialchars($schedule['subject']); ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($schedule['start_time'] && $schedule['end_time']): ?>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-clock"></i> 
                                            <?php echo date('H:i', strtotime($schedule['start_time'])); ?> - 
                                            <?php echo date('H:i', strtotime($schedule['end_time'])); ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($schedule['room']): ?>
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-door-open"></i> 
                                            <?php echo htmlspecialchars($schedule['room']); ?>
                                        </small>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <div class="btn-group btn-group-sm">
                                            <?php if (hasPermission('attendance_mark') || hasPermission('attendance_view')): ?>
                                            <a href="../attendance/mark.php?schedule_id=<?php echo $schedule['id']; ?>" 
                                               class="btn btn-outline-success" title="Điểm danh">
                                                <i class="fas fa-clipboard-check"></i>
                                            </a>
                                            <?php endif; ?>
                                            
                                            <?php if (hasPermission('schedule_manage')): ?>
                                            <a href="edit.php?id=<?php echo $schedule['id']; ?>" 
                                               class="btn btn-outline-primary" title="Chỉnh sửa">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            
                                            <button type="button" class="btn btn-outline-danger" 
                                                    title="Xóa" onclick="deleteSchedule(<?php echo $schedule['id']; ?>, '<?php echo htmlspecialchars($schedule['class_name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <?php if ($schedule['notes']): ?>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                data-bs-toggle="popover" 
                                                data-bs-content="<?php echo htmlspecialchars($schedule['notes']); ?>" 
                                                title="Ghi chú">
                                            <i class="fas fa-sticky-note"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="schedule_id" id="deleteScheduleId">
</form>

<script>
function deleteSchedule(scheduleId, className) {
    if (confirm(`Bạn có chắc chắn muốn xóa lịch học của lớp "${className}"?\n\nLưu ý: Tất cả dữ liệu điểm danh liên quan cũng sẽ bị xóa!`)) {
        document.getElementById('deleteScheduleId').value = scheduleId;
        document.getElementById('deleteForm').submit();
    }
}

// Auto-submit form when date changes
document.querySelector('input[name="date"]').addEventListener('change', function() {
    this.form.submit();
});

// Auto-submit form when filters change
document.querySelectorAll('select[name="class"], select[name="teacher"], select[name="time_slot"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});
</script>

<?php include '../../includes/footer.php'; ?>
