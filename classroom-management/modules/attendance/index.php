<?php
/**
 * Điểm danh học sinh
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = new Auth();
$auth->requirePermission('attendance_mark');

$pageTitle = 'Điểm danh';
$breadcrumb = generateBreadcrumb([
    ['title' => 'Dashboard', 'url' => '../../index.php'],
    ['title' => 'Điểm danh', 'url' => '#']
]);

// Lấy lịch học hôm nay
$today = date('Y-m-d');
$userRole = $_SESSION['user_role'];
$userId = $_SESSION['user_id'];

// Xây dựng query dựa trên role
$whereConditions = ['s.schedule_date = ?', "s.status = 'scheduled'"];
$params = [$today];

// Nếu là giáo viên thì chỉ xem lịch của mình
if ($userRole === 'teacher') {
    $whereConditions[] = 's.teacher_id = ?';
    $params[] = $userId;
}

$whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

$schedulesQuery = "
    SELECT s.*, c.class_name, c.class_code, u.full_name as teacher_name,
           CASE s.time_slot
               WHEN 'morning' THEN 'Buổi sáng'
               WHEN 'afternoon' THEN 'Buổi trưa'
               WHEN 'evening' THEN 'Buổi chiều'
           END as time_slot_name,
           (SELECT COUNT(*) FROM students st WHERE st.class_id = s.class_id AND st.status = 'active') as total_students,
           (SELECT COUNT(*) FROM attendance a WHERE a.schedule_id = s.id) as marked_students
    FROM schedules s
    JOIN classes c ON s.class_id = c.id
    JOIN users u ON s.teacher_id = u.id
    $whereClause
    ORDER BY s.time_slot, c.class_name
";

$schedules = $database->select($schedulesQuery, $params);

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clipboard-check"></i> Điểm danh ngày <?php echo formatDate($today); ?>
                </h5>
            </div>
            
            <div class="card-body">
                <?php if (empty($schedules)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Không có lịch học nào hôm nay</h5>
                    <p class="text-muted">
                        <?php if ($userRole === 'teacher'): ?>
                        Bạn không có lịch dạy nào trong ngày hôm nay.
                        <?php else: ?>
                        Chưa có lịch học nào được lên lịch cho ngày hôm nay.
                        <?php endif; ?>
                    </p>
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($schedules as $schedule): ?>
                    <div class="col-lg-6 col-xl-4 mb-4">
                        <div class="card h-100 border-primary">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chalkboard"></i> <?php echo htmlspecialchars($schedule['class_name']); ?>
                                    </h6>
                                    <span class="badge bg-light text-dark">
                                        <?php echo $schedule['time_slot_name']; ?>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-chalkboard-teacher"></i> 
                                        <?php echo htmlspecialchars($schedule['teacher_name']); ?>
                                    </small>
                                </div>
                                
                                <?php if ($schedule['subject']): ?>
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-book"></i> 
                                        <?php echo htmlspecialchars($schedule['subject']); ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($schedule['start_time'] && $schedule['end_time']): ?>
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i> 
                                        <?php echo date('H:i', strtotime($schedule['start_time'])); ?> - 
                                        <?php echo date('H:i', strtotime($schedule['end_time'])); ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($schedule['room']): ?>
                                <div class="mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-door-open"></i> 
                                        <?php echo htmlspecialchars($schedule['room']); ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                                
                                <!-- Attendance Progress -->
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <small class="text-muted">Tiến độ điểm danh</small>
                                        <small class="text-muted">
                                            <?php echo $schedule['marked_students']; ?>/<?php echo $schedule['total_students']; ?>
                                        </small>
                                    </div>
                                    
                                    <?php 
                                    $progress = $schedule['total_students'] > 0 ? 
                                        ($schedule['marked_students'] / $schedule['total_students']) * 100 : 0;
                                    $progressClass = $progress == 100 ? 'bg-success' : ($progress > 0 ? 'bg-warning' : 'bg-secondary');
                                    ?>
                                    
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar <?php echo $progressClass; ?>" 
                                             style="width: <?php echo $progress; ?>%"></div>
                                    </div>
                                </div>
                                
                                <!-- Status Badge -->
                                <?php if ($schedule['marked_students'] == $schedule['total_students'] && $schedule['total_students'] > 0): ?>
                                <div class="mb-3">
                                    <span class="badge bg-success">
                                        <i class="fas fa-check"></i> Đã điểm danh xong
                                    </span>
                                </div>
                                <?php elseif ($schedule['marked_students'] > 0): ?>
                                <div class="mb-3">
                                    <span class="badge bg-warning">
                                        <i class="fas fa-clock"></i> Đang điểm danh
                                    </span>
                                </div>
                                <?php else: ?>
                                <div class="mb-3">
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-hourglass-start"></i> Chưa điểm danh
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="card-footer">
                                <div class="d-grid gap-2">
                                    <a href="mark.php?schedule_id=<?php echo $schedule['id']; ?>" 
                                       class="btn btn-primary">
                                        <i class="fas fa-clipboard-check"></i> 
                                        <?php echo $schedule['marked_students'] > 0 ? 'Tiếp tục điểm danh' : 'Bắt đầu điểm danh'; ?>
                                    </a>
                                    
                                    <?php if ($schedule['marked_students'] > 0): ?>
                                    <a href="view.php?schedule_id=<?php echo $schedule['id']; ?>" 
                                       class="btn btn-outline-info btn-sm">
                                        <i class="fas fa-eye"></i> Xem kết quả
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Quick Stats -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-chart-pie"></i> Thống kê nhanh
                                </h6>
                                
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-number text-primary"><?php echo count($schedules); ?></div>
                                            <div class="stat-label">Tổng lịch học</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-number text-success">
                                                <?php echo count(array_filter($schedules, function($s) { 
                                                    return $s['marked_students'] == $s['total_students'] && $s['total_students'] > 0; 
                                                })); ?>
                                            </div>
                                            <div class="stat-label">Đã hoàn thành</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-number text-warning">
                                                <?php echo count(array_filter($schedules, function($s) { 
                                                    return $s['marked_students'] > 0 && $s['marked_students'] < $s['total_students']; 
                                                })); ?>
                                            </div>
                                            <div class="stat-label">Đang thực hiện</div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="stat-card">
                                            <div class="stat-number text-secondary">
                                                <?php echo count(array_filter($schedules, function($s) { 
                                                    return $s['marked_students'] == 0; 
                                                })); ?>
                                            </div>
                                            <div class="stat-label">Chưa bắt đầu</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Additional Features -->
<?php if (hasPermission('attendance_view')): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-history"></i> Lịch sử điểm danh
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="history.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-calendar-alt"></i> Xem lịch sử điểm danh
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="reports.php" class="btn btn-outline-success w-100">
                            <i class="fas fa-chart-bar"></i> Báo cáo thống kê
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Auto-refresh page every 5 minutes to update attendance progress
setInterval(function() {
    location.reload();
}, 300000); // 5 minutes

// Show notification for incomplete attendance
document.addEventListener('DOMContentLoaded', function() {
    const incompleteSchedules = <?php echo count(array_filter($schedules, function($s) { 
        return $s['marked_students'] < $s['total_students']; 
    })); ?>;
    
    if (incompleteSchedules > 0) {
        // You can add notification logic here
        console.log(`Còn ${incompleteSchedules} lịch học chưa điểm danh xong`);
    }
});
</script>

<?php include '../../includes/footer.php'; ?>
