<?php
/**
 * Danh sách học sinh
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = new Auth();
$auth->requirePermission('students_view');

$pageTitle = 'Danh sách học sinh';
$breadcrumb = generateBreadcrumb([
    ['title' => 'Dashboard', 'url' => '../../index.php'],
    ['title' => 'Học sinh', 'url' => '#']
]);

// Xử lý tìm kiếm và lọc
$search = sanitizeInput($_GET['search'] ?? '');
$classFilter = sanitizeInput($_GET['class'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));

// Xây dựng query
$whereConditions = [];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(s.full_name LIKE ? OR s.student_code LIKE ? OR s.parent_name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($classFilter)) {
    $whereConditions[] = "s.class_id = ?";
    $params[] = $classFilter;
}

if (!empty($statusFilter)) {
    $whereConditions[] = "s.status = ?";
    $params[] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Đếm tổng số bản ghi
$countQuery = "SELECT COUNT(*) FROM students s $whereClause";
$totalRecords = $database->count($countQuery, $params);
$totalPages = ceil($totalRecords / RECORDS_PER_PAGE);

// Lấy dữ liệu học sinh
$offset = ($page - 1) * RECORDS_PER_PAGE;
$studentsQuery = "
    SELECT s.*, c.class_name, c.class_code,
           TIMESTAMPDIFF(YEAR, s.date_of_birth, CURDATE()) as age
    FROM students s
    LEFT JOIN classes c ON s.class_id = c.id
    $whereClause
    ORDER BY s.created_at DESC
    LIMIT " . RECORDS_PER_PAGE . " OFFSET $offset
";

$students = $database->select($studentsQuery, $params);

// Lấy danh sách lớp cho filter
$classes = $database->select("SELECT id, class_name FROM classes WHERE status = 'active' ORDER BY class_name");

// Xử lý xóa học sinh
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    if (!hasPermission('students_manage')) {
        showAlert('Bạn không có quyền xóa học sinh!', 'error');
    } else {
        $studentId = intval($_POST['student_id']);
        
        try {
            $database->beginTransaction();
            
            // Xóa điểm danh liên quan
            $database->execute("DELETE FROM attendance WHERE student_id = ?", [$studentId]);
            
            // Xóa học sinh
            $database->execute("DELETE FROM students WHERE id = ?", [$studentId]);
            
            $database->commit();
            
            $auth->logActivity($_SESSION['user_id'], 'delete', 'students', $studentId);
            showAlert('Xóa học sinh thành công!', 'success');
            
            // Refresh trang
            redirectTo($_SERVER['REQUEST_URI']);
        } catch (Exception $e) {
            $database->rollback();
            error_log("Delete Student Error: " . $e->getMessage());
            showAlert('Có lỗi xảy ra khi xóa học sinh!', 'error');
        }
    }
}

include '../../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user-graduate"></i> Danh sách học sinh
                    <span class="badge bg-primary ms-2"><?php echo number_format($totalRecords); ?></span>
                </h5>
                <div>
                    <?php if (hasPermission('students_manage')): ?>
                    <a href="add.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Thêm học sinh
                    </a>
                    <a href="import.php" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Import Excel
                    </a>
                    <?php endif; ?>
                    <button type="button" class="btn btn-info" onclick="exportTableToExcel('studentsTable', 'danh-sach-hoc-sinh')">
                        <i class="fas fa-download"></i> Export Excel
                    </button>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Filters -->
                <form method="GET" class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label class="form-label">Tìm kiếm</label>
                        <input type="text" class="form-control" name="search" 
                               value="<?php echo htmlspecialchars($search); ?>" 
                               placeholder="Tên, mã học sinh, tên phụ huynh...">
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Lớp học</label>
                        <select name="class" class="form-select">
                            <option value="">-- Tất cả lớp --</option>
                            <?php foreach ($classes as $class): ?>
                            <option value="<?php echo $class['id']; ?>" 
                                    <?php echo $classFilter == $class['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($class['class_name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Trạng thái</label>
                        <select name="status" class="form-select">
                            <option value="">-- Tất cả trạng thái --</option>
                            <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>Đang học</option>
                            <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>Tạm nghỉ</option>
                            <option value="graduated" <?php echo $statusFilter === 'graduated' ? 'selected' : ''; ?>>Đã tốt nghiệp</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Tìm kiếm
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- Students Table -->
                <?php if (empty($students)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Không tìm thấy học sinh nào</h5>
                    <?php if (hasPermission('students_manage')): ?>
                    <a href="add.php" class="btn btn-primary mt-3">
                        <i class="fas fa-plus"></i> Thêm học sinh đầu tiên
                    </a>
                    <?php endif; ?>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover" id="studentsTable">
                        <thead>
                            <tr>
                                <th>Mã HS</th>
                                <th>Họ tên</th>
                                <th>Tuổi</th>
                                <th>Lớp</th>
                                <th>Phụ huynh</th>
                                <th>SĐT</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $student): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($student['student_code']); ?></strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="student-avatar me-2">
                                            <?php echo strtoupper(substr($student['full_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <strong><?php echo htmlspecialchars($student['full_name']); ?></strong>
                                            <?php if ($student['gender']): ?>
                                            <br><small class="text-muted">
                                                <?php echo $student['gender'] === 'male' ? 'Nam' : ($student['gender'] === 'female' ? 'Nữ' : 'Khác'); ?>
                                            </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php echo $student['age'] ? $student['age'] . ' tuổi' : 'N/A'; ?>
                                </td>
                                <td>
                                    <?php if ($student['class_name']): ?>
                                    <span class="badge bg-info"><?php echo htmlspecialchars($student['class_name']); ?></span>
                                    <?php else: ?>
                                    <span class="text-muted">Chưa phân lớp</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($student['parent_name'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($student['parent_phone'] ?? 'N/A'); ?></td>
                                <td>
                                    <?php
                                    $statusClass = [
                                        'active' => 'success',
                                        'inactive' => 'warning', 
                                        'graduated' => 'secondary'
                                    ][$student['status']] ?? 'secondary';
                                    
                                    $statusText = [
                                        'active' => 'Đang học',
                                        'inactive' => 'Tạm nghỉ',
                                        'graduated' => 'Đã tốt nghiệp'
                                    ][$student['status']] ?? $student['status'];
                                    ?>
                                    <span class="badge bg-<?php echo $statusClass; ?>">
                                        <?php echo $statusText; ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="view.php?id=<?php echo $student['id']; ?>" 
                                           class="btn btn-outline-info" title="Xem chi tiết">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        
                                        <?php if (hasPermission('students_manage')): ?>
                                        <a href="edit.php?id=<?php echo $student['id']; ?>" 
                                           class="btn btn-outline-primary" title="Chỉnh sửa">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        
                                        <button type="button" class="btn btn-outline-danger" 
                                                title="Xóa" onclick="deleteStudent(<?php echo $student['id']; ?>, '<?php echo htmlspecialchars($student['full_name']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php echo generatePagination($page, $totalPages, 'index.php', [
                    'search' => $search,
                    'class' => $classFilter,
                    'status' => $statusFilter
                ]); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Delete Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="student_id" id="deleteStudentId">
</form>

<script>
function deleteStudent(studentId, studentName) {
    if (confirm(`Bạn có chắc chắn muốn xóa học sinh "${studentName}"?\n\nLưu ý: Tất cả dữ liệu điểm danh liên quan cũng sẽ bị xóa!`)) {
        document.getElementById('deleteStudentId').value = studentId;
        document.getElementById('deleteForm').submit();
    }
}

// Auto-submit form when filters change
document.querySelectorAll('select[name="class"], select[name="status"]').forEach(function(select) {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});
</script>

<?php include '../../includes/footer.php'; ?>
