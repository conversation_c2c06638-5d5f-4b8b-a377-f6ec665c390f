<?php
/**
 * Thêm học sinh mới
 */

require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

$auth = new Auth();
$auth->requirePermission('students_manage');

$pageTitle = 'Thêm học sinh';
$breadcrumb = generateBreadcrumb([
    ['title' => 'Dashboard', 'url' => '../../index.php'],
    ['title' => 'Học sinh', 'url' => 'index.php'],
    ['title' => 'Thêm học sinh', 'url' => '#']
]);

// Lấy danh sách lớp
$classes = $database->select("SELECT id, class_name FROM classes WHERE status = 'active' ORDER BY class_name");

$errors = [];
$formData = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy dữ liệu form
    $formData = [
        'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
        'date_of_birth' => sanitizeInput($_POST['date_of_birth'] ?? ''),
        'gender' => sanitizeInput($_POST['gender'] ?? ''),
        'parent_name' => sanitizeInput($_POST['parent_name'] ?? ''),
        'parent_phone' => sanitizeInput($_POST['parent_phone'] ?? ''),
        'address' => sanitizeInput($_POST['address'] ?? ''),
        'google_drive_link' => sanitizeInput($_POST['google_drive_link'] ?? ''),
        'class_id' => intval($_POST['class_id'] ?? 0),
        'status' => sanitizeInput($_POST['status'] ?? 'active')
    ];
    
    // Validate dữ liệu
    if (empty($formData['full_name'])) {
        $errors['full_name'] = 'Vui lòng nhập họ tên học sinh!';
    }
    
    if (empty($formData['date_of_birth'])) {
        $errors['date_of_birth'] = 'Vui lòng nhập ngày sinh!';
    } elseif (!strtotime($formData['date_of_birth'])) {
        $errors['date_of_birth'] = 'Ngày sinh không hợp lệ!';
    }
    
    if (empty($formData['parent_name'])) {
        $errors['parent_name'] = 'Vui lòng nhập tên phụ huynh!';
    }
    
    if (!empty($formData['parent_phone']) && !validatePhone($formData['parent_phone'])) {
        $errors['parent_phone'] = 'Số điện thoại không hợp lệ!';
    }
    
    if (!empty($formData['google_drive_link']) && !filter_var($formData['google_drive_link'], FILTER_VALIDATE_URL)) {
        $errors['google_drive_link'] = 'Link Google Drive không hợp lệ!';
    }
    
    if ($formData['class_id'] > 0) {
        // Kiểm tra lớp có tồn tại không
        $classExists = $database->selectOne("SELECT id FROM classes WHERE id = ? AND status = 'active'", [$formData['class_id']]);
        if (!$classExists) {
            $errors['class_id'] = 'Lớp học không tồn tại!';
        }
    }
    
    // Nếu không có lỗi thì thêm học sinh
    if (empty($errors)) {
        try {
            $database->beginTransaction();
            
            // Tạo mã học sinh tự động
            $studentCode = generateStudentCode();
            
            // Thêm học sinh
            $insertQuery = "
                INSERT INTO students (student_code, full_name, date_of_birth, gender, parent_name, 
                                    parent_phone, address, google_drive_link, class_id, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $params = [
                $studentCode,
                $formData['full_name'],
                $formData['date_of_birth'],
                $formData['gender'] ?: null,
                $formData['parent_name'],
                $formData['parent_phone'] ?: null,
                $formData['address'] ?: null,
                $formData['google_drive_link'] ?: null,
                $formData['class_id'] ?: null,
                $formData['status']
            ];
            
            if ($database->execute($insertQuery, $params)) {
                $studentId = $database->lastInsertId();
                
                $database->commit();
                
                // Ghi log
                $auth->logActivity($_SESSION['user_id'], 'create', 'students', $studentId, null, $formData);
                
                showAlert('Thêm học sinh thành công! Mã học sinh: ' . $studentCode, 'success');
                redirectTo('view.php?id=' . $studentId);
            } else {
                throw new Exception('Không thể thêm học sinh!');
            }
        } catch (Exception $e) {
            $database->rollback();
            error_log("Add Student Error: " . $e->getMessage());
            showAlert('Có lỗi xảy ra khi thêm học sinh!', 'error');
        }
    }
}

include '../../includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus"></i> Thêm học sinh mới
                </h5>
            </div>
            
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <!-- Thông tin cơ bản -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user"></i> Thông tin cơ bản
                            </h6>
                            
                            <div class="mb-3">
                                <label for="full_name" class="form-label">
                                    Họ tên <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control <?php echo isset($errors['full_name']) ? 'is-invalid' : ''; ?>" 
                                       id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($formData['full_name'] ?? ''); ?>" 
                                       placeholder="Nhập họ tên học sinh" required>
                                <?php if (isset($errors['full_name'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['full_name']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="date_of_birth" class="form-label">
                                    Ngày sinh <span class="text-danger">*</span>
                                </label>
                                <input type="date" class="form-control <?php echo isset($errors['date_of_birth']) ? 'is-invalid' : ''; ?>" 
                                       id="date_of_birth" name="date_of_birth" 
                                       value="<?php echo htmlspecialchars($formData['date_of_birth'] ?? ''); ?>" required>
                                <?php if (isset($errors['date_of_birth'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['date_of_birth']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="gender" class="form-label">Giới tính</label>
                                <select class="form-select" id="gender" name="gender">
                                    <option value="">-- Chọn giới tính --</option>
                                    <option value="male" <?php echo ($formData['gender'] ?? '') === 'male' ? 'selected' : ''; ?>>Nam</option>
                                    <option value="female" <?php echo ($formData['gender'] ?? '') === 'female' ? 'selected' : ''; ?>>Nữ</option>
                                    <option value="other" <?php echo ($formData['gender'] ?? '') === 'other' ? 'selected' : ''; ?>>Khác</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="class_id" class="form-label">Lớp học</label>
                                <select class="form-select <?php echo isset($errors['class_id']) ? 'is-invalid' : ''; ?>" 
                                        id="class_id" name="class_id">
                                    <option value="">-- Chọn lớp --</option>
                                    <?php foreach ($classes as $class): ?>
                                    <option value="<?php echo $class['id']; ?>" 
                                            <?php echo ($formData['class_id'] ?? 0) == $class['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($class['class_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <?php if (isset($errors['class_id'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['class_id']; ?></div>
                                <?php endif; ?>
                                <div class="form-text">Có thể để trống và phân lớp sau</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">Trạng thái</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" <?php echo ($formData['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Đang học</option>
                                    <option value="inactive" <?php echo ($formData['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Tạm nghỉ</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- Thông tin phụ huynh -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-users"></i> Thông tin phụ huynh
                            </h6>
                            
                            <div class="mb-3">
                                <label for="parent_name" class="form-label">
                                    Tên phụ huynh <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control <?php echo isset($errors['parent_name']) ? 'is-invalid' : ''; ?>" 
                                       id="parent_name" name="parent_name" 
                                       value="<?php echo htmlspecialchars($formData['parent_name'] ?? ''); ?>" 
                                       placeholder="Nhập tên phụ huynh" required>
                                <?php if (isset($errors['parent_name'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['parent_name']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="parent_phone" class="form-label">Số điện thoại</label>
                                <input type="tel" class="form-control <?php echo isset($errors['parent_phone']) ? 'is-invalid' : ''; ?>" 
                                       id="parent_phone" name="parent_phone" 
                                       value="<?php echo htmlspecialchars($formData['parent_phone'] ?? ''); ?>" 
                                       placeholder="Nhập số điện thoại">
                                <?php if (isset($errors['parent_phone'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['parent_phone']; ?></div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">Địa chỉ</label>
                                <textarea class="form-control" id="address" name="address" rows="3" 
                                          placeholder="Nhập địa chỉ"><?php echo htmlspecialchars($formData['address'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="google_drive_link" class="form-label">
                                    <i class="fab fa-google-drive"></i> Link Google Drive
                                </label>
                                <input type="url" class="form-control <?php echo isset($errors['google_drive_link']) ? 'is-invalid' : ''; ?>" 
                                       id="google_drive_link" name="google_drive_link" 
                                       value="<?php echo htmlspecialchars($formData['google_drive_link'] ?? ''); ?>" 
                                       placeholder="https://drive.google.com/...">
                                <?php if (isset($errors['google_drive_link'])): ?>
                                <div class="invalid-feedback"><?php echo $errors['google_drive_link']; ?></div>
                                <?php endif; ?>
                                <div class="form-text">Link thư mục chứa hồ sơ học sinh</div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                        
                        <div>
                            <button type="reset" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-undo"></i> Làm mới
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu học sinh
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-generate student code preview
document.getElementById('full_name').addEventListener('input', function() {
    // You can add preview functionality here if needed
});

// Validate form before submit
document.querySelector('form').addEventListener('submit', function(e) {
    const fullName = document.getElementById('full_name').value.trim();
    const dateOfBirth = document.getElementById('date_of_birth').value;
    const parentName = document.getElementById('parent_name').value.trim();
    
    if (!fullName || !dateOfBirth || !parentName) {
        e.preventDefault();
        alert('Vui lòng điền đầy đủ thông tin bắt buộc!');
        return false;
    }
    
    // Show loading
    const submitBtn = this.querySelector('button[type="submit"]');
    const hideLoading = showLoading(submitBtn);
});
</script>

<?php include '../../includes/footer.php'; ?>
