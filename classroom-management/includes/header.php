<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_PATH; ?>css/style.css" rel="stylesheet">
    
    <!-- Additional CSS -->
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?php echo $css; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <?php if (isLoggedIn()): ?>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-school"></i> <?php echo APP_NAME; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    
                    <?php if (hasPermission('students_manage') || hasPermission('students_view')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-graduate"></i> Học sinh
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="modules/students/index.php">Danh sách học sinh</a></li>
                            <?php if (hasPermission('students_manage')): ?>
                            <li><a class="dropdown-item" href="modules/students/add.php">Thêm học sinh</a></li>
                            <li><a class="dropdown-item" href="modules/students/import.php">Import Excel</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('classes_manage')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chalkboard"></i> Lớp học
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="modules/classes/index.php">Danh sách lớp</a></li>
                            <li><a class="dropdown-item" href="modules/classes/add.php">Thêm lớp</a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('schedule_manage') || hasPermission('schedule_view')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calendar-alt"></i> Lịch học
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="modules/schedule/index.php">Xem lịch học</a></li>
                            <?php if (hasPermission('schedule_manage')): ?>
                            <li><a class="dropdown-item" href="modules/schedule/add.php">Tạo lịch học</a></li>
                            <li><a class="dropdown-item" href="modules/schedule/calendar.php">Lịch tuần</a></li>
                            <?php endif; ?>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('attendance_mark') || hasPermission('attendance_view')): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="modules/attendance/index.php">
                            <i class="fas fa-clipboard-check"></i> Điểm danh
                        </a>
                    </li>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('finance_manage')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-money-bill-wave"></i> Thu chi
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="modules/finance/index.php">Danh sách thu chi</a></li>
                            <li><a class="dropdown-item" href="modules/finance/add.php">Thêm thu chi</a></li>
                            <li><a class="dropdown-item" href="modules/finance/reports.php">Báo cáo</a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('assets_manage')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes"></i> Tài sản
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="modules/assets/index.php">Danh sách tài sản</a></li>
                            <li><a class="dropdown-item" href="modules/assets/add.php">Thêm tài sản</a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('users_manage')): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users"></i> Người dùng
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="modules/users/index.php">Danh sách người dùng</a></li>
                            <li><a class="dropdown-item" href="modules/users/add.php">Thêm người dùng</a></li>
                        </ul>
                    </li>
                    <?php endif; ?>
                </ul>
                
                <!-- User menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-bell"></i>
                            <span class="badge bg-danger" id="notification-count">0</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" id="notification-dropdown">
                            <li><h6 class="dropdown-header">Thông báo</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="modules/notifications/index.php">Xem tất cả</a></li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?php echo $_SESSION['full_name']; ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="modules/profile/index.php">
                                <i class="fas fa-user-edit"></i> Hồ sơ cá nhân
                            </a></li>
                            <li><a class="dropdown-item" href="modules/profile/change-password.php">
                                <i class="fas fa-key"></i> Đổi mật khẩu
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt"></i> Đăng xuất
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <?php endif; ?>
    
    <!-- Main content -->
    <div class="<?php echo isLoggedIn() ? 'container-fluid mt-4' : 'container'; ?>">
        <?php
        // Hiển thị thông báo
        $alert = getAlert();
        if ($alert):
        ?>
        <div class="alert alert-<?php echo $alert['type'] === 'error' ? 'danger' : $alert['type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $alert['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if (isset($breadcrumb) && isLoggedIn()): ?>
        <div class="row">
            <div class="col-12">
                <?php echo $breadcrumb; ?>
            </div>
        </div>
        <?php endif; ?>
