<?php
/**
 * Helper Functions
 * <PERSON><PERSON><PERSON> hàm tiện ích chung cho hệ thống
 */

/**
 * Tạo mã học sinh tự động
 */
function generateStudentCode() {
    global $database;
    
    $year = date('Y');
    $prefix = 'HS' . $year;
    
    // Lấy số thứ tự cao nhất trong năm
    $query = "SELECT student_code FROM students WHERE student_code LIKE ? ORDER BY student_code DESC LIMIT 1";
    $result = $database->selectOne($query, [$prefix . '%']);
    
    if ($result) {
        $lastNumber = intval(substr($result['student_code'], -4));
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * Tạo mã tài sản tự động
 */
function generateAssetCode($category = 'TS') {
    global $database;
    
    $year = date('Y');
    $prefix = $category . $year;
    
    $query = "SELECT asset_code FROM assets WHERE asset_code LIKE ? ORDER BY asset_code DESC LIMIT 1";
    $result = $database->selectOne($query, [$prefix . '%']);
    
    if ($result) {
        $lastNumber = intval(substr($result['asset_code'], -4));
        $newNumber = $lastNumber + 1;
    } else {
        $newNumber = 1;
    }
    
    return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * Upload file
 */
function uploadFile($file, $uploadDir = 'uploads/', $allowedTypes = null) {
    if (!$allowedTypes) {
        $allowedTypes = ALLOWED_EXTENSIONS;
    }
    
    $uploadPath = UPLOAD_PATH . $uploadDir;
    
    // Tạo thư mục nếu chưa tồn tại
    if (!file_exists($uploadPath)) {
        mkdir($uploadPath, 0777, true);
    }
    
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmp = $file['tmp_name'];
    $fileError = $file['error'];
    
    // Kiểm tra lỗi upload
    if ($fileError !== UPLOAD_ERR_OK) {
        return [
            'success' => false,
            'message' => 'Có lỗi xảy ra khi upload file!'
        ];
    }
    
    // Kiểm tra kích thước file
    if ($fileSize > MAX_FILE_SIZE) {
        return [
            'success' => false,
            'message' => 'File quá lớn! Kích thước tối đa: ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB'
        ];
    }
    
    // Kiểm tra định dạng file
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if (!in_array($fileExt, $allowedTypes)) {
        return [
            'success' => false,
            'message' => 'Định dạng file không được hỗ trợ!'
        ];
    }
    
    // Tạo tên file mới để tránh trùng lặp
    $newFileName = uniqid() . '_' . time() . '.' . $fileExt;
    $destination = $uploadPath . $newFileName;
    
    // Di chuyển file
    if (move_uploaded_file($fileTmp, $destination)) {
        return [
            'success' => true,
            'message' => 'Upload file thành công!',
            'file_name' => $newFileName,
            'file_path' => $uploadDir . $newFileName
        ];
    } else {
        return [
            'success' => false,
            'message' => 'Không thể upload file!'
        ];
    }
}

/**
 * Xóa file
 */
function deleteFile($filePath) {
    $fullPath = UPLOAD_PATH . $filePath;
    if (file_exists($fullPath)) {
        return unlink($fullPath);
    }
    return false;
}

/**
 * Tạo breadcrumb
 */
function generateBreadcrumb($items) {
    $html = '<nav aria-label="breadcrumb"><ol class="breadcrumb">';
    
    foreach ($items as $index => $item) {
        if ($index === count($items) - 1) {
            $html .= '<li class="breadcrumb-item active" aria-current="page">' . $item['title'] . '</li>';
        } else {
            $html .= '<li class="breadcrumb-item"><a href="' . $item['url'] . '">' . $item['title'] . '</a></li>';
        }
    }
    
    $html .= '</ol></nav>';
    return $html;
}

/**
 * Tạo pagination
 */
function generatePagination($currentPage, $totalPages, $baseUrl, $params = []) {
    if ($totalPages <= 1) return '';
    
    $html = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';
    
    // Previous button
    if ($currentPage > 1) {
        $prevUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $currentPage - 1]));
        $html .= '<li class="page-item"><a class="page-link" href="' . $prevUrl . '">Trước</a></li>';
    }
    
    // Page numbers
    $start = max(1, $currentPage - 2);
    $end = min($totalPages, $currentPage + 2);
    
    if ($start > 1) {
        $firstUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => 1]));
        $html .= '<li class="page-item"><a class="page-link" href="' . $firstUrl . '">1</a></li>';
        if ($start > 2) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    for ($i = $start; $i <= $end; $i++) {
        if ($i == $currentPage) {
            $html .= '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
        } else {
            $pageUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $i]));
            $html .= '<li class="page-item"><a class="page-link" href="' . $pageUrl . '">' . $i . '</a></li>';
        }
    }
    
    if ($end < $totalPages) {
        if ($end < $totalPages - 1) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        $lastUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $totalPages]));
        $html .= '<li class="page-item"><a class="page-link" href="' . $lastUrl . '">' . $totalPages . '</a></li>';
    }
    
    // Next button
    if ($currentPage < $totalPages) {
        $nextUrl = $baseUrl . '?' . http_build_query(array_merge($params, ['page' => $currentPage + 1]));
        $html .= '<li class="page-item"><a class="page-link" href="' . $nextUrl . '">Sau</a></li>';
    }
    
    $html .= '</ul></nav>';
    return $html;
}

/**
 * Validate email
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Vietnam)
 */
function validatePhone($phone) {
    $pattern = '/^(0|\+84)[3|5|7|8|9][0-9]{8}$/';
    return preg_match($pattern, $phone);
}

/**
 * Tạo select options
 */
function generateSelectOptions($options, $selectedValue = '', $emptyOption = true) {
    $html = '';
    
    if ($emptyOption) {
        $html .= '<option value="">-- Chọn --</option>';
    }
    
    foreach ($options as $value => $text) {
        $selected = ($value == $selectedValue) ? 'selected' : '';
        $html .= '<option value="' . htmlspecialchars($value) . '" ' . $selected . '>' . htmlspecialchars($text) . '</option>';
    }
    
    return $html;
}

/**
 * Tạo checkbox/radio options
 */
function generateCheckboxOptions($name, $options, $selectedValues = [], $type = 'checkbox') {
    $html = '';
    
    foreach ($options as $value => $text) {
        $checked = in_array($value, $selectedValues) ? 'checked' : '';
        $html .= '<div class="form-check">';
        $html .= '<input class="form-check-input" type="' . $type . '" name="' . $name . '[]" value="' . htmlspecialchars($value) . '" ' . $checked . '>';
        $html .= '<label class="form-check-label">' . htmlspecialchars($text) . '</label>';
        $html .= '</div>';
    }
    
    return $html;
}

/**
 * Tính tuổi từ ngày sinh
 */
function calculateAge($birthDate) {
    $today = new DateTime();
    $birth = new DateTime($birthDate);
    return $today->diff($birth)->y;
}

/**
 * Chuyển đổi ngày từ định dạng d/m/Y sang Y-m-d
 */
function convertDateFormat($date, $fromFormat = 'd/m/Y', $toFormat = 'Y-m-d') {
    $dateObj = DateTime::createFromFormat($fromFormat, $date);
    return $dateObj ? $dateObj->format($toFormat) : false;
}

/**
 * Lấy danh sách ngày trong tuần
 */
function getWeekDays($startDate = null) {
    if (!$startDate) {
        $startDate = date('Y-m-d', strtotime('monday this week'));
    }
    
    $days = [];
    for ($i = 0; $i < 7; $i++) {
        $date = date('Y-m-d', strtotime($startDate . ' +' . $i . ' days'));
        $days[] = [
            'date' => $date,
            'day_name' => date('l', strtotime($date)),
            'day_name_vi' => [
                'Monday' => 'Thứ 2',
                'Tuesday' => 'Thứ 3', 
                'Wednesday' => 'Thứ 4',
                'Thursday' => 'Thứ 5',
                'Friday' => 'Thứ 6',
                'Saturday' => 'Thứ 7',
                'Sunday' => 'Chủ nhật'
            ][date('l', strtotime($date))]
        ];
    }
    
    return $days;
}

/**
 * Export dữ liệu ra Excel
 */
function exportToExcel($data, $headers, $filename) {
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment;filename="' . $filename . '.xls"');
    header('Cache-Control: max-age=0');
    
    echo '<table border="1">';
    
    // Headers
    echo '<tr>';
    foreach ($headers as $header) {
        echo '<th>' . htmlspecialchars($header) . '</th>';
    }
    echo '</tr>';
    
    // Data
    foreach ($data as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            echo '<td>' . htmlspecialchars($cell) . '</td>';
        }
        echo '</tr>';
    }
    
    echo '</table>';
    exit;
}

/**
 * Gửi email
 */
function sendEmail($to, $subject, $message, $from = null) {
    if (!$from) {
        $from = '<EMAIL>';
    }
    
    $headers = [
        'From' => $from,
        'Reply-To' => $from,
        'Content-Type' => 'text/html; charset=UTF-8'
    ];
    
    return mail($to, $subject, $message, $headers);
}

/**
 * Tạo token ngẫu nhiên
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Kiểm tra CSRF token
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Tạo CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken();
    }
    return $_SESSION['csrf_token'];
}
?>
