    </div> <!-- End main content container -->
    
    <!-- Footer -->
    <?php if (isLoggedIn()): ?>
    <footer class="bg-light text-center text-muted py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-md-start">
                    <p class="mb-0">
                        © <?php echo date('Y'); ?> <?php echo APP_NAME; ?> v<?php echo APP_VERSION; ?>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        Phát triển bởi <strong>Augment Code</strong>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    <?php endif; ?>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="<?php echo ASSETS_PATH; ?>js/app.js"></script>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <script>
        // Global JavaScript functions
        
        // Confirm delete action
        function confirmDelete(message = 'Bạn có chắc chắn muốn xóa?') {
            return confirm(message);
        }
        
        // Show loading spinner
        function showLoading(element) {
            const originalText = element.innerHTML;
            element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
            element.disabled = true;
            
            return function() {
                element.innerHTML = originalText;
                element.disabled = false;
            };
        }
        
        // Format currency input
        function formatCurrency(input) {
            let value = input.value.replace(/[^\d]/g, '');
            value = value.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            input.value = value;
        }
        
        // Auto-resize textarea
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
        
        // Initialize popovers
        document.addEventListener('DOMContentLoaded', function() {
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        });
        
        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
                alerts.forEach(function(alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);
        });
        
        // Load notifications count
        <?php if (isLoggedIn()): ?>
        function loadNotificationCount() {
            fetch('api/notifications-count.php')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('notification-count');
                    if (badge) {
                        badge.textContent = data.count;
                        badge.style.display = data.count > 0 ? 'inline' : 'none';
                    }
                })
                .catch(error => console.error('Error loading notifications:', error));
        }
        
        // Load notifications dropdown
        function loadNotifications() {
            fetch('api/notifications-recent.php')
                .then(response => response.json())
                .then(data => {
                    const dropdown = document.getElementById('notification-dropdown');
                    if (dropdown && data.notifications) {
                        let html = '<li><h6 class="dropdown-header">Thông báo mới</h6></li>';
                        html += '<li><hr class="dropdown-divider"></li>';
                        
                        if (data.notifications.length === 0) {
                            html += '<li><span class="dropdown-item-text text-muted">Không có thông báo mới</span></li>';
                        } else {
                            data.notifications.forEach(notification => {
                                html += `<li><a class="dropdown-item" href="modules/notifications/view.php?id=${notification.id}">
                                    <div class="fw-bold">${notification.title}</div>
                                    <small class="text-muted">${notification.created_at}</small>
                                </a></li>`;
                            });
                        }
                        
                        html += '<li><hr class="dropdown-divider"></li>';
                        html += '<li><a class="dropdown-item text-center" href="modules/notifications/index.php">Xem tất cả</a></li>';
                        
                        dropdown.innerHTML = html;
                    }
                })
                .catch(error => console.error('Error loading notifications:', error));
        }
        
        // Load notifications on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadNotificationCount();
            loadNotifications();
            
            // Refresh notifications every 30 seconds
            setInterval(function() {
                loadNotificationCount();
                loadNotifications();
            }, 30000);
        });
        <?php endif; ?>
        
        // Form validation helpers
        function validateEmail(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }
        
        function validatePhone(phone) {
            const re = /^(0|\+84)[3|5|7|8|9][0-9]{8}$/;
            return re.test(phone);
        }
        
        // Date helpers
        function formatDate(date, format = 'dd/mm/yyyy') {
            const d = new Date(date);
            const day = String(d.getDate()).padStart(2, '0');
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const year = d.getFullYear();
            
            return format.replace('dd', day).replace('mm', month).replace('yyyy', year);
        }
        
        // Search functionality
        function initializeSearch(inputId, tableId) {
            const searchInput = document.getElementById(inputId);
            const table = document.getElementById(tableId);
            
            if (searchInput && table) {
                searchInput.addEventListener('keyup', function() {
                    const filter = this.value.toLowerCase();
                    const rows = table.getElementsByTagName('tr');
                    
                    for (let i = 1; i < rows.length; i++) {
                        const row = rows[i];
                        const cells = row.getElementsByTagName('td');
                        let found = false;
                        
                        for (let j = 0; j < cells.length; j++) {
                            if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                                found = true;
                                break;
                            }
                        }
                        
                        row.style.display = found ? '' : 'none';
                    }
                });
            }
        }
        
        // Export table to Excel
        function exportTableToExcel(tableId, filename = 'export') {
            const table = document.getElementById(tableId);
            if (!table) return;
            
            const wb = XLSX.utils.table_to_book(table);
            XLSX.writeFile(wb, filename + '.xlsx');
        }
        
        // Print functionality
        function printElement(elementId) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>In ấn</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                        <style>
                            @media print {
                                .no-print { display: none !important; }
                                body { font-size: 12px; }
                            }
                        </style>
                    </head>
                    <body>
                        ${element.innerHTML}
                        <script>
                            window.onload = function() {
                                window.print();
                                window.close();
                            }
                        </script>
                    </body>
                </html>
            `);
            printWindow.document.close();
        }
    </script>
</body>
</html>
