<?php
/**
 * Authentication Class
 * Xử lý đăng nhập, đăng xuất và phân quyền
 */

require_once '../config/database.php';
require_once '../config/config.php';

class Auth {
    private $db;
    
    public function __construct() {
        global $database;
        $this->db = $database;
    }

    /**
     * Đăng nhập người dùng
     */
    public function login($username, $password) {
        try {
            $query = "SELECT id, username, password, full_name, email, role, status 
                     FROM users WHERE username = ? AND status = 'active'";
            
            $user = $this->db->selectOne($query, [$username]);
            
            if ($user && password_verify($password, $user['password'])) {
                // Tạo session
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['last_activity'] = time();
                
                // Ghi log đăng nhập
                $this->logActivity($user['id'], 'login', 'users', $user['id']);
                
                return [
                    'success' => true,
                    'message' => 'Đăng nhập thành công!',
                    'user' => $user
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Tên đăng nhập hoặc mật khẩu không đúng!'
                ];
            }
        } catch (Exception $e) {
            error_log("Login Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đăng nhập!'
            ];
        }
    }

    /**
     * Đăng xuất người dùng
     */
    public function logout() {
        if (isset($_SESSION['user_id'])) {
            $this->logActivity($_SESSION['user_id'], 'logout', 'users', $_SESSION['user_id']);
        }
        
        session_destroy();
        return [
            'success' => true,
            'message' => 'Đăng xuất thành công!'
        ];
    }

    /**
     * Kiểm tra đăng nhập
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
    }

    /**
     * Kiểm tra quyền truy cập
     */
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $userRole = $_SESSION['user_role'];
        return in_array($permission, PERMISSIONS[$userRole] ?? []);
    }

    /**
     * Kiểm tra role
     */
    public function hasRole($role) {
        return isset($_SESSION['user_role']) && $_SESSION['user_role'] === $role;
    }

    /**
     * Lấy thông tin user hiện tại
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        $query = "SELECT id, username, full_name, email, phone, role, created_at 
                 FROM users WHERE id = ?";
        
        return $this->db->selectOne($query, [$_SESSION['user_id']]);
    }

    /**
     * Đổi mật khẩu
     */
    public function changePassword($userId, $oldPassword, $newPassword) {
        try {
            // Kiểm tra mật khẩu cũ
            $query = "SELECT password FROM users WHERE id = ?";
            $user = $this->db->selectOne($query, [$userId]);
            
            if (!$user || !password_verify($oldPassword, $user['password'])) {
                return [
                    'success' => false,
                    'message' => 'Mật khẩu cũ không đúng!'
                ];
            }
            
            // Cập nhật mật khẩu mới
            $hashedPassword = password_hash($newPassword, HASH_ALGO);
            $updateQuery = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
            
            if ($this->db->execute($updateQuery, [$hashedPassword, $userId])) {
                $this->logActivity($userId, 'change_password', 'users', $userId);
                
                return [
                    'success' => true,
                    'message' => 'Đổi mật khẩu thành công!'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi đổi mật khẩu!'
                ];
            }
        } catch (Exception $e) {
            error_log("Change Password Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình đổi mật khẩu!'
            ];
        }
    }

    /**
     * Reset mật khẩu (dành cho admin)
     */
    public function resetPassword($userId, $newPassword = null) {
        try {
            if (!$this->hasPermission('users_manage')) {
                return [
                    'success' => false,
                    'message' => 'Bạn không có quyền thực hiện thao tác này!'
                ];
            }
            
            // Tạo mật khẩu ngẫu nhiên nếu không được cung cấp
            if (!$newPassword) {
                $newPassword = $this->generateRandomPassword();
            }
            
            $hashedPassword = password_hash($newPassword, HASH_ALGO);
            $query = "UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?";
            
            if ($this->db->execute($query, [$hashedPassword, $userId])) {
                $this->logActivity($_SESSION['user_id'], 'reset_password', 'users', $userId);
                
                return [
                    'success' => true,
                    'message' => 'Reset mật khẩu thành công!',
                    'new_password' => $newPassword
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi reset mật khẩu!'
                ];
            }
        } catch (Exception $e) {
            error_log("Reset Password Error: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình reset mật khẩu!'
            ];
        }
    }

    /**
     * Tạo mật khẩu ngẫu nhiên
     */
    private function generateRandomPassword($length = 8) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $password;
    }

    /**
     * Ghi log hoạt động
     */
    public function logActivity($userId, $action, $tableName, $recordId = null, $oldValues = null, $newValues = null) {
        try {
            $query = "INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $userId,
                $action,
                $tableName,
                $recordId,
                $oldValues ? json_encode($oldValues) : null,
                $newValues ? json_encode($newValues) : null,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ];
            
            $this->db->execute($query, $params);
        } catch (Exception $e) {
            error_log("Log Activity Error: " . $e->getMessage());
        }
    }

    /**
     * Kiểm tra session timeout
     */
    public function checkSessionTimeout() {
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
                $this->logout();
                redirectTo('login.php?timeout=1');
            }
        }
        $_SESSION['last_activity'] = time();
    }

    /**
     * Middleware kiểm tra đăng nhập
     */
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            redirectTo('login.php');
        }
        $this->checkSessionTimeout();
    }

    /**
     * Middleware kiểm tra quyền
     */
    public function requirePermission($permission) {
        $this->requireLogin();
        
        if (!$this->hasPermission($permission)) {
            showAlert('Bạn không có quyền truy cập trang này!', 'error');
            redirectTo('index.php');
        }
    }
}
?>
