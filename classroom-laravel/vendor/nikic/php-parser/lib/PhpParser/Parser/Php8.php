<?php declare(strict_types=1);

namespace Php<PERSON><PERSON><PERSON>\Parser;

use Php<PERSON><PERSON><PERSON>\Error;
use Php<PERSON>arser\Modifiers;
use Php<PERSON>arser\Node;
use PhpParser\Node\Expr;
use Php<PERSON>arser\Node\Name;
use Php<PERSON><PERSON>er\Node\Scalar;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Stmt;

/* This is an automatically GENERATED file, which should not be manually edited.
 * Instead edit one of the following:
 *  * the grammar file grammar/php.y
 *  * the skeleton file grammar/parser.template
 *  * the preprocessing script grammar/rebuildParsers.php
 */
class Php8 extends \PhpParser\ParserAbstract
{
    public const YYERRTOK = 256;
    public const T_THROW = 257;
    public const T_INCLUDE = 258;
    public const T_INCLUDE_ONCE = 259;
    public const T_EVAL = 260;
    public const T_REQUIRE = 261;
    public const T_REQUIRE_ONCE = 262;
    public const T_LOGICAL_OR = 263;
    public const T_LOGICAL_XOR = 264;
    public const T_LOGICAL_AND = 265;
    public const T_PRINT = 266;
    public const T_YIELD = 267;
    public const T_DOUBLE_ARROW = 268;
    public const T_YIELD_FROM = 269;
    public const T_PLUS_EQUAL = 270;
    public const T_MINUS_EQUAL = 271;
    public const T_MUL_EQUAL = 272;
    public const T_DIV_EQUAL = 273;
    public const T_CONCAT_EQUAL = 274;
    public const T_MOD_EQUAL = 275;
    public const T_AND_EQUAL = 276;
    public const T_OR_EQUAL = 277;
    public const T_XOR_EQUAL = 278;
    public const T_SL_EQUAL = 279;
    public const T_SR_EQUAL = 280;
    public const T_POW_EQUAL = 281;
    public const T_COALESCE_EQUAL = 282;
    public const T_COALESCE = 283;
    public const T_BOOLEAN_OR = 284;
    public const T_BOOLEAN_AND = 285;
    public const T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG = 286;
    public const T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG = 287;
    public const T_IS_EQUAL = 288;
    public const T_IS_NOT_EQUAL = 289;
    public const T_IS_IDENTICAL = 290;
    public const T_IS_NOT_IDENTICAL = 291;
    public const T_SPACESHIP = 292;
    public const T_IS_SMALLER_OR_EQUAL = 293;
    public const T_IS_GREATER_OR_EQUAL = 294;
    public const T_SL = 295;
    public const T_SR = 296;
    public const T_INSTANCEOF = 297;
    public const T_INC = 298;
    public const T_DEC = 299;
    public const T_INT_CAST = 300;
    public const T_DOUBLE_CAST = 301;
    public const T_STRING_CAST = 302;
    public const T_ARRAY_CAST = 303;
    public const T_OBJECT_CAST = 304;
    public const T_BOOL_CAST = 305;
    public const T_UNSET_CAST = 306;
    public const T_POW = 307;
    public const T_NEW = 308;
    public const T_CLONE = 309;
    public const T_EXIT = 310;
    public const T_IF = 311;
    public const T_ELSEIF = 312;
    public const T_ELSE = 313;
    public const T_ENDIF = 314;
    public const T_LNUMBER = 315;
    public const T_DNUMBER = 316;
    public const T_STRING = 317;
    public const T_STRING_VARNAME = 318;
    public const T_VARIABLE = 319;
    public const T_NUM_STRING = 320;
    public const T_INLINE_HTML = 321;
    public const T_ENCAPSED_AND_WHITESPACE = 322;
    public const T_CONSTANT_ENCAPSED_STRING = 323;
    public const T_ECHO = 324;
    public const T_DO = 325;
    public const T_WHILE = 326;
    public const T_ENDWHILE = 327;
    public const T_FOR = 328;
    public const T_ENDFOR = 329;
    public const T_FOREACH = 330;
    public const T_ENDFOREACH = 331;
    public const T_DECLARE = 332;
    public const T_ENDDECLARE = 333;
    public const T_AS = 334;
    public const T_SWITCH = 335;
    public const T_MATCH = 336;
    public const T_ENDSWITCH = 337;
    public const T_CASE = 338;
    public const T_DEFAULT = 339;
    public const T_BREAK = 340;
    public const T_CONTINUE = 341;
    public const T_GOTO = 342;
    public const T_FUNCTION = 343;
    public const T_FN = 344;
    public const T_CONST = 345;
    public const T_RETURN = 346;
    public const T_TRY = 347;
    public const T_CATCH = 348;
    public const T_FINALLY = 349;
    public const T_USE = 350;
    public const T_INSTEADOF = 351;
    public const T_GLOBAL = 352;
    public const T_STATIC = 353;
    public const T_ABSTRACT = 354;
    public const T_FINAL = 355;
    public const T_PRIVATE = 356;
    public const T_PROTECTED = 357;
    public const T_PUBLIC = 358;
    public const T_READONLY = 359;
    public const T_PUBLIC_SET = 360;
    public const T_PROTECTED_SET = 361;
    public const T_PRIVATE_SET = 362;
    public const T_VAR = 363;
    public const T_UNSET = 364;
    public const T_ISSET = 365;
    public const T_EMPTY = 366;
    public const T_HALT_COMPILER = 367;
    public const T_CLASS = 368;
    public const T_TRAIT = 369;
    public const T_INTERFACE = 370;
    public const T_ENUM = 371;
    public const T_EXTENDS = 372;
    public const T_IMPLEMENTS = 373;
    public const T_OBJECT_OPERATOR = 374;
    public const T_NULLSAFE_OBJECT_OPERATOR = 375;
    public const T_LIST = 376;
    public const T_ARRAY = 377;
    public const T_CALLABLE = 378;
    public const T_CLASS_C = 379;
    public const T_TRAIT_C = 380;
    public const T_METHOD_C = 381;
    public const T_FUNC_C = 382;
    public const T_PROPERTY_C = 383;
    public const T_LINE = 384;
    public const T_FILE = 385;
    public const T_START_HEREDOC = 386;
    public const T_END_HEREDOC = 387;
    public const T_DOLLAR_OPEN_CURLY_BRACES = 388;
    public const T_CURLY_OPEN = 389;
    public const T_PAAMAYIM_NEKUDOTAYIM = 390;
    public const T_NAMESPACE = 391;
    public const T_NS_C = 392;
    public const T_DIR = 393;
    public const T_NS_SEPARATOR = 394;
    public const T_ELLIPSIS = 395;
    public const T_NAME_FULLY_QUALIFIED = 396;
    public const T_NAME_QUALIFIED = 397;
    public const T_NAME_RELATIVE = 398;
    public const T_ATTRIBUTE = 399;

    protected int $tokenToSymbolMapSize = 400;
    protected int $actionTableSize = 1289;
    protected int $gotoTableSize = 710;

    protected int $invalidSymbol = 172;
    protected int $errorSymbol = 1;
    protected int $defaultAction = -32766;
    protected int $unexpectedTokenRule = 32767;

    protected int $YY2TBLSTATE = 444;
    protected int $numNonLeafStates = 756;

    protected array $symbolToName = array(
        "EOF",
        "error",
        "T_THROW",
        "T_INCLUDE",
        "T_INCLUDE_ONCE",
        "T_EVAL",
        "T_REQUIRE",
        "T_REQUIRE_ONCE",
        "','",
        "T_LOGICAL_OR",
        "T_LOGICAL_XOR",
        "T_LOGICAL_AND",
        "T_PRINT",
        "T_YIELD",
        "T_DOUBLE_ARROW",
        "T_YIELD_FROM",
        "'='",
        "T_PLUS_EQUAL",
        "T_MINUS_EQUAL",
        "T_MUL_EQUAL",
        "T_DIV_EQUAL",
        "T_CONCAT_EQUAL",
        "T_MOD_EQUAL",
        "T_AND_EQUAL",
        "T_OR_EQUAL",
        "T_XOR_EQUAL",
        "T_SL_EQUAL",
        "T_SR_EQUAL",
        "T_POW_EQUAL",
        "T_COALESCE_EQUAL",
        "'?'",
        "':'",
        "T_COALESCE",
        "T_BOOLEAN_OR",
        "T_BOOLEAN_AND",
        "'|'",
        "'^'",
        "T_AMPERSAND_NOT_FOLLOWED_BY_VAR_OR_VARARG",
        "T_AMPERSAND_FOLLOWED_BY_VAR_OR_VARARG",
        "T_IS_EQUAL",
        "T_IS_NOT_EQUAL",
        "T_IS_IDENTICAL",
        "T_IS_NOT_IDENTICAL",
        "T_SPACESHIP",
        "'<'",
        "T_IS_SMALLER_OR_EQUAL",
        "'>'",
        "T_IS_GREATER_OR_EQUAL",
        "'.'",
        "T_SL",
        "T_SR",
        "'+'",
        "'-'",
        "'*'",
        "'/'",
        "'%'",
        "'!'",
        "T_INSTANCEOF",
        "'~'",
        "T_INC",
        "T_DEC",
        "T_INT_CAST",
        "T_DOUBLE_CAST",
        "T_STRING_CAST",
        "T_ARRAY_CAST",
        "T_OBJECT_CAST",
        "T_BOOL_CAST",
        "T_UNSET_CAST",
        "'@'",
        "T_POW",
        "'['",
        "T_NEW",
        "T_CLONE",
        "T_EXIT",
        "T_IF",
        "T_ELSEIF",
        "T_ELSE",
        "T_ENDIF",
        "T_LNUMBER",
        "T_DNUMBER",
        "T_STRING",
        "T_STRING_VARNAME",
        "T_VARIABLE",
        "T_NUM_STRING",
        "T_INLINE_HTML",
        "T_ENCAPSED_AND_WHITESPACE",
        "T_CONSTANT_ENCAPSED_STRING",
        "T_ECHO",
        "T_DO",
        "T_WHILE",
        "T_ENDWHILE",
        "T_FOR",
        "T_ENDFOR",
        "T_FOREACH",
        "T_ENDFOREACH",
        "T_DECLARE",
        "T_ENDDECLARE",
        "T_AS",
        "T_SWITCH",
        "T_MATCH",
        "T_ENDSWITCH",
        "T_CASE",
        "T_DEFAULT",
        "T_BREAK",
        "T_CONTINUE",
        "T_GOTO",
        "T_FUNCTION",
        "T_FN",
        "T_CONST",
        "T_RETURN",
        "T_TRY",
        "T_CATCH",
        "T_FINALLY",
        "T_USE",
        "T_INSTEADOF",
        "T_GLOBAL",
        "T_STATIC",
        "T_ABSTRACT",
        "T_FINAL",
        "T_PRIVATE",
        "T_PROTECTED",
        "T_PUBLIC",
        "T_READONLY",
        "T_PUBLIC_SET",
        "T_PROTECTED_SET",
        "T_PRIVATE_SET",
        "T_VAR",
        "T_UNSET",
        "T_ISSET",
        "T_EMPTY",
        "T_HALT_COMPILER",
        "T_CLASS",
        "T_TRAIT",
        "T_INTERFACE",
        "T_ENUM",
        "T_EXTENDS",
        "T_IMPLEMENTS",
        "T_OBJECT_OPERATOR",
        "T_NULLSAFE_OBJECT_OPERATOR",
        "T_LIST",
        "T_ARRAY",
        "T_CALLABLE",
        "T_CLASS_C",
        "T_TRAIT_C",
        "T_METHOD_C",
        "T_FUNC_C",
        "T_PROPERTY_C",
        "T_LINE",
        "T_FILE",
        "T_START_HEREDOC",
        "T_END_HEREDOC",
        "T_DOLLAR_OPEN_CURLY_BRACES",
        "T_CURLY_OPEN",
        "T_PAAMAYIM_NEKUDOTAYIM",
        "T_NAMESPACE",
        "T_NS_C",
        "T_DIR",
        "T_NS_SEPARATOR",
        "T_ELLIPSIS",
        "T_NAME_FULLY_QUALIFIED",
        "T_NAME_QUALIFIED",
        "T_NAME_RELATIVE",
        "T_ATTRIBUTE",
        "';'",
        "']'",
        "'('",
        "')'",
        "'{'",
        "'}'",
        "'`'",
        "'\"'",
        "'$'"
    );

    protected array $tokenToSymbol = array(
            0,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,   56,  170,  172,  171,   55,  172,  172,
          165,  166,   53,   51,    8,   52,   48,   54,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,   31,  163,
           44,   16,   46,   30,   68,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,   70,  172,  164,   36,  172,  169,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  167,   35,  168,   58,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,  172,  172,  172,  172,
          172,  172,  172,  172,  172,  172,    1,    2,    3,    4,
            5,    6,    7,    9,   10,   11,   12,   13,   14,   15,
           17,   18,   19,   20,   21,   22,   23,   24,   25,   26,
           27,   28,   29,   32,   33,   34,   37,   38,   39,   40,
           41,   42,   43,   45,   47,   49,   50,   57,   59,   60,
           61,   62,   63,   64,   65,   66,   67,   69,   71,   72,
           73,   74,   75,   76,   77,   78,   79,   80,   81,   82,
           83,   84,   85,   86,   87,   88,   89,   90,   91,   92,
           93,   94,   95,   96,   97,   98,   99,  100,  101,  102,
          103,  104,  105,  106,  107,  108,  109,  110,  111,  112,
          113,  114,  115,  116,  117,  118,  119,  120,  121,  122,
          123,  124,  125,  126,  127,  128,  129,  130,  131,  132,
          133,  134,  135,  136,  137,  138,  139,  140,  141,  142,
          143,  144,  145,  146,  147,  148,  149,  150,  151,  152,
          153,  154,  155,  156,  157,  158,  159,  160,  161,  162
    );

    protected array $action = array(
          126,  127,  128,  573,  129,  130,  959,  768,  769,  770,
          131,   38,  852,  493,  569, 1380,-32766,-32766,-32766,    0,
          843, 1138, 1139, 1140, 1134, 1133, 1132, 1141, 1135, 1136,
         1137,-32766,-32766,-32766,  854,  762,  761,-32766, 1049,-32766,
        -32766,-32766,-32766,-32766,-32766,-32766,-32767,-32767,-32767,-32767,
        -32767, 1009,-32766,-32766,-32766,  771, 1138, 1139, 1140, 1134,
         1133, 1132, 1141, 1135, 1136, 1137,  387,  388,  447,  263,
          132,  390,  775,  776,  777,  778,  432,  848,  433, 1317,
         -571,   36,  246,   47,  292,  832,  779,  780,  781,  782,
          783,  784,  785,  786,  787,  788,  808,  574,  809,  810,
          811,  812,  800,  801,  345,  346,  803,  804,  789,  790,
          791,  793,  794,  795,  360,  835,  836,  837,  838,  839,
          575,    2,  297, -333,  796,  797,  576,  577,  236,  820,
          818,  819,  831,  815,  816,   26, -195,  578,  579,  814,
          580,  581,  582,  583,  324,  584,  585, -571, -571,  494,
          298,  299,  817,  586,  587,   35,  133,  849,  126,  127,
          128,  573,  129,  130, 1082,  768,  769,  770,  131,   38,
        -32766,  134,  738, 1042, 1041, 1040, 1046, 1043, 1044, 1045,
        -32766,-32766,-32766, 1010,  104,  105,  106,  107,  108,  880,
          275,  881,-32766,  762,  761, 1058,  853,-32766,-32766,-32766,
          143,-32766,  109,-32766,-32766,-32766,-32766,-32766,-32766,-32766,
        -32766,  479,  480,  771,-32766,-32766,-32766, 1058,-32766,  291,
        -32766,-32766,-32766,-32766,-32766, -194,  249,  263,  132,  390,
          775,  776,  777,  778,-32766,-32766,  433,-32766,-32766,-32766,
        -32766,  291,  851,  832,  779,  780,  781,  782,  783,  784,
          785,  786,  787,  788,  808,  574,  809,  810,  811,  812,
          800,  801,  345,  346,  803,  804,  789,  790,  791,  793,
          794,  795,  360,  835,  836,  837,  838,  839,  575,  962,
         -274, -333,  796,  797,  576,  577,  843,  820,  818,  819,
          831,  815,  816, 1305, -195,  578,  579,  814,  580,  581,
          582,  583,  308,  584,  585,-32766,   82,   83,   84,  -85,
          817,  586,  587,  161,  146,  792,  763,  764,  765,  766,
          767, 1346,  768,  769,  770,  805,  806,   37,  961,   85,
           86,   87,   88,   89,   90,   91,   92,   93,   94,   95,
           96,   97,   98,   99,  100,  101,  102,  103,  104,  105,
          106,  107,  108,  310,  275,-32766,-32766,-32766,-32767,-32767,
        -32767,-32767,  101,  102,  103, 1112,  109,  319,  625,  751,
          771,-32766,-32766,-32766,  852,  -85,-32766, 1111,-32766,-32766,
        -32766,  389,  388, -194,  772,  773,  774,  775,  776,  777,
          778,  432,-32766,  841,-32766,-32766, 1390,  341, 1285, 1391,
          832,  779,  780,  781,  782,  783,  784,  785,  786,  787,
          788,  808,  830,  809,  810,  811,  812,  800,  801,  802,
          829,  803,  804,  789,  790,  791,  793,  794,  795,  834,
          835,  836,  837,  838,  839,  840, 1081,  433, -568,  796,
          797,  798,  799, 1365,  820,  818,  819,  831,  815,  816,
         1364,   24,  807,  813,  814,  821,  822,  824,  823,  138,
          825,  826, 1154,  322,  342,  286,  743,  817,  828,  827,
           49,   50,   51,  525,   52,   53, 1127, -110,  375,  852,
           54,   55, -110,   56, -110,  619,-32766,   81,  381,  304,
         1361,  322, -110, -110, -110, -110, -110, -110, -110, -110,
         -110, -110, -110,  157,  938, -568, -568,  292,  978,  979,
          397, 1058,  845,  980,  451,  286, 1280, 1279, 1281,   57,
           58, -568,  974,  148,   59, 1113,   60,  243,  244,   61,
           62,   63,   64,   65,   66,   67,   68,-32766,   28,  265,
           69,  449,  526,  452, -347, 1055, 1311, 1312,  527,  938,
          852, 1055,  762,  761, 1309,   42,   20,  528,  938,  529,
          938,  530,   74,  531,  453,  701,  532,  533,  322,  843,
         1058,   44,   45,  455,  384,  383, 1058,   46,  534,  728,
         -569,  235,  454,  373,  340,  858, 1285,  928,  729,  938,
         1271, -567,  847,-32766,  282,  536,  537,  538,  149,  725,
          282,  702,  -78, -373, 1278, -373,  151,  540,  541,  -58,
         1297, 1298, 1299, 1300, 1302, 1294, 1295,  296, 1058,  730,
        -32766,-32766,-32766, 1301, 1296,  703,  704, 1280, 1279, 1281,
          297, -565,  928,   70, -154, -154, -154,  317,  318,  322,
         1276,  928,  291,  928, 1280, 1279, 1281, -569, -569, -154,
          282, -154,  -57, -154,  753, -154,  139, 1055, -567, -567,
          322,  762,  761, -569, 1057,  382,  940,  852,  152,  399,
          723,    7,  928,  153, -567, -575,  978,  979,  469,  470,
          471,  535, 1058, 1280, 1279, 1281, -574,  102,  103,  914,
          974, -110, -110, -110,   28,  266,-32766,-32766, -565, -565,
          668,   21, -110, -110,  687,  688,  852, -110,  155,   48,
         1309,  940,  385,  386, -565,  723, -110,  147,  415,  880,
          940,  881,  940,   33,  723,-32766,  723, -154,  391,  392,
           32,  110,  111,  112,  113,  114,  115,  116,  117,  118,
          119,  120,  121,  122,  659,  660, 1271,  297,  762,  761,
           74,  995,  123,  938,  124,  723,  322,   -4,  938, -607,
          938, -607,  135,  540,  541,  136, 1297, 1298, 1299, 1300,
         1302, 1294, 1295, 1187, 1189, -307,  300,  301, -566, 1301,
         1296,  762,  761,  733, -565,-32766,  142,  156,  158,   72,
          740, 1278,  380,  159,  318,  322,  160,  -87,-32766,-32766,
        -32766,  287,-32766, -303,-32766,  -84,-32766,  -78,  280,-32766,
          -73,  275,  387,  388,-32766,-32766,-32766,  -72,-32766,  -71,
        -32766,-32766,  432,  -70, 1278,  -69,-32766,  429,   28,  265,
          -68,-32766,-32766,-32766,  -67,-32766,  928,-32766,-32766,-32766,
          852,  928,-32766,  928, 1309, -566, -566,-32766,-32766,-32766,
          -66, -565, -565,-32766,-32766,  -65,  -46,  -18,  140,-32766,
          429, -566,  274,  382,  283,  445,  739, -565,  297,   73,
          295,-32766,  742, -573,  978,  979,  937,  145,  281,  535,
         1271,   28,  266,  284,  285,  330,  109,  539,  974, -110,
         -110, -110,  288,  852,-32766,-32766,-32766, 1309,  541,  125,
         1297, 1298, 1299, 1300, 1302, 1294, 1295,  293,  294,  144,
          955,   11,  843, 1301, 1296,  940,  712,  697,  590,  723,
          940, 1392,  940,   72,  723,   -4,  723,-32766,  318,  322,
          -50,  690,  305, 1271,  852, 1145,  714,  669,  975,  302,
          309,  657,  303, 1316, -531, 1318,  596,  674,-32766, -521,
            8,  541,  297, 1297, 1298, 1299, 1300, 1302, 1294, 1295,
           10,  476,  504,  137,   40,   27, 1301, 1296,  675,  691,
          623, 1244,  957,-32766,  379,  851,   72,   34,   41, 1278,
          322,  318,  322,  748,    0,  749,-32766,-32766,-32766, -277,
        -32766,    0,-32766,  871,-32766,    0,    0,-32766, 1306,    0,
            0,    0,-32766,-32766,-32766,  938,-32766,    0,-32766,-32766,
            0,    0, 1278,    0,-32766,  429,    0,  919,    0,-32766,
        -32766,-32766, 1019,-32766,  996,-32766,-32766,-32766,  938, 1003,
        -32766,  863, 1310,    0,    0,-32766,-32766,-32766,  993,-32766,
         1004,-32766,-32766,  917,  991, 1278, 1116,-32766,  429, 1119,
         1120, 1117,-32766,-32766,-32766, 1156,-32766, 1118,-32766,-32766,
        -32766, 1124, -601,-32766, 1333, 1350, 1383,  499,-32766,-32766,
        -32766,  662,-32766, -600,-32766,-32766, -599, -575, 1278,  603,
        -32766,  429, -574, -573, -572,-32766,-32766,-32766,  928,-32766,
         -515,-32766,-32766,-32766,    1,   29,-32766, -275,   30,   39,
           43,-32766,-32766,-32766, -252, -252, -252,-32766,-32766,   71,
          382,  928,   75,-32766,  429,   76,   77,   78, 1285,   79,
           80,  978,  979,  141,  150,-32766,  535, -251, -251, -251,
         -274,  154,  241,  382,  914,  974, -110, -110, -110,  326,
          361,  362,  363,  364,  978,  979,  365,  366,  -16,  535,
          367,  368,  369,  370,  371,  374,  446,  914,  974, -110,
         -110, -110,-32766,   13,  568,  372,    0,  940, 1278,   14,
          414,  723, -252,   15,   16,-32766,-32766,-32766,   18,-32766,
          355,-32766,  413,-32766,  495,  496,-32766,  503,  506,  507,
          940,-32766,-32766,-32766,  723, -251,  508,-32766,-32766,  852,
          509,  513,  514,-32766,  429,  515,  522,  601,  707, 1084,
         1227, 1307, 1083, 1064, 1266,-32766, 1060, -279, -102,   12,
           17,   22,  313,  412,  615,  620,  648,  713, 1231, 1284,
         1228, 1362,    0,  316, -110, -110,  376,  724,  727, -110,
          731,  732,  734,  735,  736,  737,  741,  753, -110,  726,
          754,    0,  418,  745,  915, 1387,    0,-32766, 1389,  874,
          873,  968, 1011, 1388,  967,  965,  966,  969, 1259,  948,
          958,  946, 1155, 1151, 1105, 1001, 1002,  646, 1386,  297,
         1344, 1359,   74,    0,    0,    0,    0,    0,  322
    );

    protected array $actionCheck = array(
            2,    3,    4,    5,    6,    7,    1,    9,   10,   11,
           12,   13,   82,   31,   85,   85,    9,   10,   11,    0,
           80,  116,  117,  118,  119,  120,  121,  122,  123,  124,
          125,    9,   10,   11,    1,   37,   38,   30,    1,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,   31,   30,    9,   10,   57,  116,  117,  118,  119,
          120,  121,  122,  123,  124,  125,  106,  107,  108,   71,
           72,   73,   74,   75,   76,   77,  116,   80,   80,  150,
           70,  151,  152,   70,   30,   87,   88,   89,   90,   91,
           92,   93,   94,   95,   96,   97,   98,   99,  100,  101,
          102,  103,  104,  105,  106,  107,  108,  109,  110,  111,
          112,  113,  114,  115,  116,  117,  118,  119,  120,  121,
          122,    8,  162,    8,  126,  127,  128,  129,   14,  131,
          132,  133,  134,  135,  136,    8,    8,  139,  140,  141,
          142,  143,  144,  145,   70,  147,  148,  137,  138,  167,
          137,  138,  154,  155,  156,    8,  158,  160,    2,    3,
            4,    5,    6,    7,  166,    9,   10,   11,   12,   13,
          116,    8,  167,  119,  120,  121,  122,  123,  124,  125,
            9,   10,   11,  163,   51,   52,   53,   54,   55,  106,
           57,  108,  116,   37,   38,  141,  163,    9,   10,   11,
            8,   30,   69,   32,   33,   34,   35,   36,   37,   38,
          116,  137,  138,   57,    9,   10,   11,  141,   30,  165,
           32,   33,   34,   35,   36,    8,    8,   71,   72,   73,
           74,   75,   76,   77,  140,   30,   80,   32,   33,   34,
           35,  165,  159,   87,   88,   89,   90,   91,   92,   93,
           94,   95,   96,   97,   98,   99,  100,  101,  102,  103,
          104,  105,  106,  107,  108,  109,  110,  111,  112,  113,
          114,  115,  116,  117,  118,  119,  120,  121,  122,   73,
          166,  166,  126,  127,  128,  129,   80,  131,  132,  133,
          134,  135,  136,    1,  166,  139,  140,  141,  142,  143,
          144,  145,    8,  147,  148,    9,    9,   10,   11,   31,
          154,  155,  156,   14,  158,    2,    3,    4,    5,    6,
            7,    1,    9,   10,   11,   12,   13,   30,  122,   32,
           33,   34,   35,   36,   37,   38,   39,   40,   41,   42,
           43,   44,   45,   46,   47,   48,   49,   50,   51,   52,
           53,   54,   55,    8,   57,    9,   10,   11,   44,   45,
           46,   47,   48,   49,   50,  163,   69,    8,   52,  167,
           57,    9,   10,   11,   82,   97,   30,    1,   32,   33,
           34,  106,  107,  166,   71,   72,   73,   74,   75,   76,
           77,  116,   30,   80,   32,   33,   80,    8,    1,   83,
           87,   88,   89,   90,   91,   92,   93,   94,   95,   96,
           97,   98,   99,  100,  101,  102,  103,  104,  105,  106,
          107,  108,  109,  110,  111,  112,  113,  114,  115,  116,
          117,  118,  119,  120,  121,  122,    1,   80,   70,  126,
          127,  128,  129,    1,  131,  132,  133,  134,  135,  136,
            8,  101,  139,  140,  141,  142,  143,  144,  145,  167,
          147,  148,  163,  171,    8,   30,  167,  154,  155,  156,
            2,    3,    4,    5,    6,    7,  126,  101,    8,   82,
           12,   13,  106,   15,  108,    1,  116,  167,    8,  113,
            1,  171,  116,  117,  118,  119,  120,  121,  122,  123,
          124,  125,  126,   16,    1,  137,  138,   30,  117,  118,
            8,  141,   80,  122,    8,   30,  159,  160,  161,   51,
           52,  153,  131,   14,   56,  168,   58,   59,   60,   61,
           62,   63,   64,   65,   66,   67,   68,  140,   70,   71,
           72,   73,   74,    8,  168,  116,   78,   79,   80,    1,
           82,  116,   37,   38,   86,   87,   88,   89,    1,   91,
            1,   93,  165,   95,    8,   80,   98,   99,  171,   80,
          141,  103,  104,  105,  106,  107,  141,  109,  110,   31,
           70,   97,    8,  115,  116,    8,    1,   84,   31,    1,
          122,   70,  160,  116,  165,  127,  128,  129,   14,  167,
          165,  116,   16,  106,   80,  108,   14,  139,  140,   16,
          142,  143,  144,  145,  146,  147,  148,  149,  141,   31,
            9,   10,   11,  155,  156,  140,  141,  159,  160,  161,
          162,   70,   84,  165,   75,   76,   77,  169,  170,  171,
          116,   84,  165,   84,  159,  160,  161,  137,  138,   90,
          165,   92,   16,   94,  167,   96,  167,  116,  137,  138,
          171,   37,   38,  153,  140,  106,  163,   82,   14,  106,
          167,  108,   84,   14,  153,  165,  117,  118,  132,  133,
          134,  122,  141,  159,  160,  161,  165,   49,   50,  130,
          131,  132,  133,  134,   70,   71,   51,   52,  137,  138,
           75,   76,  117,  118,   75,   76,   82,  122,   14,   70,
           86,  163,  106,  107,  153,  167,  131,  101,  102,  106,
          163,  108,  163,   14,  167,  140,  167,  168,  106,  107,
           16,   17,   18,   19,   20,   21,   22,   23,   24,   25,
           26,   27,   28,   29,  111,  112,  122,  162,   37,   38,
          165,  163,   16,    1,   16,  167,  171,    0,    1,  164,
            1,  166,   16,  139,  140,   16,  142,  143,  144,  145,
          146,  147,  148,   59,   60,   35,  137,  138,   70,  155,
          156,   37,   38,   31,   70,   74,   16,   16,   16,  165,
           31,   80,  153,   16,  170,  171,   16,   31,   87,   88,
           89,   37,   91,   35,   93,   31,   95,   31,   35,   98,
           31,   57,  106,  107,  103,  104,  105,   31,   74,   31,
          109,  110,  116,   31,   80,   31,  115,  116,   70,   71,
           31,   87,   88,   89,   31,   91,   84,   93,  127,   95,
           82,   84,   98,   84,   86,  137,  138,  103,  104,  105,
           31,  137,  138,  109,  110,   31,   31,   31,   31,  115,
          116,  153,   31,  106,   31,  108,   31,  153,  162,  158,
          113,  127,   31,  165,  117,  118,   31,   31,   35,  122,
          122,   70,   71,   35,   35,   35,   69,  130,  131,  132,
          133,  134,   37,   82,    9,   10,   11,   86,  140,   14,
          142,  143,  144,  145,  146,  147,  148,   37,   37,   70,
           38,  154,   80,  155,  156,  163,   80,   77,   89,  167,
          163,   83,  163,  165,  167,  168,  167,   85,  170,  171,
           31,   94,  114,  122,   82,   82,   92,   90,  131,  135,
          135,  113,  136,  150,  153,  150,  157,   96,  140,  153,
          153,  140,  162,  142,  143,  144,  145,  146,  147,  148,
           97,   97,   97,   31,  163,  153,  155,  156,  100,  100,
          157,  169,  158,   74,  153,  159,  165,  167,  163,   80,
          171,  170,  171,  163,   -1,  163,   87,   88,   89,  166,
           91,   -1,   93,  163,   95,   -1,   -1,   98,  164,   -1,
           -1,   -1,  103,  104,  105,    1,   74,   -1,  109,  110,
           -1,   -1,   80,   -1,  115,  116,   -1,  163,   -1,   87,
           88,   89,  163,   91,  163,   93,  127,   95,    1,  163,
           98,  164,  170,   -1,   -1,  103,  104,  105,  163,   74,
          163,  109,  110,  163,  163,   80,  163,  115,  116,  163,
          163,  163,   87,   88,   89,  163,   91,  163,   93,  127,
           95,  163,  165,   98,  164,  164,  164,  102,  103,  104,
          105,  164,   74,  165,  109,  110,  165,  165,   80,   81,
          115,  116,  165,  165,  165,   87,   88,   89,   84,   91,
          165,   93,  127,   95,  165,  165,   98,  166,  165,  165,
          165,  103,  104,  105,  100,  101,  102,  109,  110,  165,
          106,   84,  165,  115,  116,  165,  165,  165,    1,  165,
          165,  117,  118,  165,  165,  127,  122,  100,  101,  102,
          166,  165,  165,  106,  130,  131,  132,  133,  134,  165,
          165,  165,  165,  165,  117,  118,  165,  165,   31,  122,
          165,  165,  165,  165,  165,  165,  165,  130,  131,  132,
          133,  134,   74,  166,  165,  165,   -1,  163,   80,  166,
          168,  167,  168,  166,  166,   87,   88,   89,  166,   91,
          166,   93,  166,   95,  166,  166,   98,  166,  166,  166,
          163,  103,  104,  105,  167,  168,  166,  109,  110,   82,
          166,  166,  166,  115,  116,  166,  166,  166,  166,  166,
          166,  166,  166,  166,  166,  127,  166,  166,  166,  166,
          166,  166,  166,  166,  166,  166,  166,  166,  166,  166,
          166,  166,   -1,  167,  117,  118,  167,  167,  167,  122,
          167,  167,  167,  167,  167,  167,  167,  167,  131,  167,
          167,   -1,  168,  168,  168,  168,   -1,  140,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  168,
          168,  168,  168,  168,  168,  168,  168,  168,  168,  162,
          168,  168,  165,   -1,   -1,   -1,   -1,   -1,  171
    );

    protected array $actionBase = array(
            0,   -2,  156,  559,  757, 1004, 1027,  485,  292,  357,
          -60,  432,  557,  752,  752,  759,  752,  548,  588,  894,
          503,  503,  503,  836,  313,  313,  836,  313,  711,  711,
          711,  711,  744,  744,  965,  965,  998,  932,  899, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088, 1088,
         1088, 1088,   33,   20,  484, 1080,  709, 1056, 1062, 1058,
         1063, 1054, 1053, 1057, 1059, 1064, 1110, 1112,  846, 1109,
         1113, 1060,  907, 1055, 1061,  892,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,  296,  885,   44,  611,  611,  611,  611,  611,
          611,  611,  611,  611,  611,  611,  611,  611,  611,  611,
          611,  611,  611,  611,  611,  624,  624,   22,   22,   22,
          362,  811,  758,  811,  811,  811,  811,  811,  811,  811,
          811,  346,  205,  188,  714,  171,  171,    7,    7,    7,
            7,    7,  376, 1117,   54,  585,  585,  314,  314,  314,
          314,  350,  515,  497,  435,  397,  -40,  638,  477,  706,
          429,  429,  541,  541,   76,   76,  541,  541,  541,  133,
          133,  370,  370,  370,  370,   83,  -71,  808,  489,  489,
          489,  489,  808,  808,  808,  808,  795,  862,  808,  808,
          808,  510,  521,  708,  645,  645,  613,  -70,  -70,  613,
          391,  -70,  320,  975,  316,  982,   94,  807,  275,  595,
           94, 1000,  368,  561,  561,  639,  561,  561,  561,  816,
          606,  816, 1052,  842,  842,  825,  779,  898, 1082, 1065,
          868, 1107,  869, 1108, 1083,  299,  546,   10,   13,   74,
          776, 1051, 1051, 1051, 1051, 1051, 1051, 1051, 1051, 1051,
         1051, 1051, 1051,  809,  515, 1052,   -3, 1105, 1106,  809,
          809,  809,  515,  515,  515,  515,  515,  515,  515,  515,
          826,  515,  515,  633,   -3,  625,  629,   -3,  850,  515,
          796,   33,   33,   33,   33,   33,   33,   33,   33,   33,
           33,   33,  -18,   33,   33,   20,    5,    5,   33,  202,
           37,    5,    5,    5,  487,    5,   33,   33,   33,  606,
          754,  789,  622,  278,  813,  217,  754,  754,  754,  115,
          114,  128,  740,  768,  563,  832,  832,  832,  853,  929,
          929,  832,  852,  832,  853,  832,  832,  929,  929,  790,
          929,  163,  506,  389,  480,  535,  929,  294,  832,  832,
          832,  832,  805,  929,  113,  556,  832,  218,  192,  832,
          832,  805,  804,  833,  806,  929,  929,  929,  805,  470,
          806,  806,  806,  820,  822,  828,  831,  359,  345,  577,
          147,  872,  831,  831,  832,  502,  828,  831,  828,  831,
          814,  831,  831,  831,  828,  831,  852,  456,  831,  771,
          574,  127,  831,  832,   19,  944,  947,  766,  950,  934,
          951,  991,  952,  954, 1070,  925,  967,  935,  955,  999,
          933,  930,  845,  736,  738,  797,  791,  919,  817,  817,
          817,  912,  917,  817,  817,  817,  817,  817,  817,  817,
          817,  736,  834,  821,  829,  976,  746,  749, 1041,  793,
         1086,  802,  975,  944,  954,  774,  935,  955,  933,  930,
          824,  819,  799,  803,  794,  792,  786,  788,  827, 1043,
          958,  801,  770, 1012,  977, 1085, 1066,  978,  981, 1016,
         1044,  830, 1045, 1087,  839, 1090, 1091,  867,  985, 1071,
          817,  911,  897,  900,  982,  918,  736,  901, 1046,  997,
          810, 1018, 1019, 1069,  864,  838,  902, 1092,  986,  987,
          988, 1073, 1074,  815, 1003,  823, 1021,  865, 1002, 1022,
         1023, 1030, 1034, 1075, 1093, 1076,  908, 1077,  854,  847,
          931,  851, 1094,  509,  848,  849,  871,  990,  584,  974,
         1078, 1084, 1095, 1035, 1036, 1039, 1096, 1097,  959,  859,
         1007,  837, 1008,  964,  861,  866,  592,  870, 1047,  773,
          843,  855,  654,  659, 1098, 1099, 1100,  966,  835,  840,
          875,  877, 1048,  764, 1050, 1101,  694,  880, 1102, 1042,
          772,  777,  586,  636,  593,  780,  844, 1079,  812,  818,
          863,  989,  777,  841,  881, 1103,  883,  886,  887, 1040,
          888, 1014, 1104,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,  468,  468,  468,
          468,  468,  468,  313,  313,  313,  313,  313,  468,  468,
          468,  468,  468,  468,  468,  313,  468,  468,  468,  313,
            0,    0,  313,    0,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  468,  468,
          468,  468,  468,  468,  468,  468,  468,  468,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  297,  297,  297,  297,  297,  297,  297,  297,  297,
          297,  524,  524,  297,  297,  297,  297,  524,  524,  524,
          524,  524,  524,  524,  524,  524,  524,  297,  297,  297,
            0,  297,  297,  297,  297,  297,  297,  297,  790,  524,
          524,  524,  524,  133,  133,  133,  133,  -95,  -95,  -95,
          524,  524,  391,  133,  524,  391,  524,  524,  524,  524,
          524,  524,  524,  524,  524,    0,    0,  524,  524,  524,
          524,   -3,  -70,  524,  852,  852,  852,  852,  524,  524,
          524,  524,  -70,  -70,  524,  524,  524,    0,    0,    0,
          133,  133,   -3,    0,    0,   -3,    0,    0,  852,  206,
          852,  206,  524,  391,  790,  442,  524,  299,    0,    0,
            0,    0,    0,    0,    0,   -3,  852,   -3,  515,  -70,
          -70,  515,  515,    5,   33,  442,  616,  616,  616,  616,
           33,    0,    0,    0,    0,    0,  606,  790,  790,  790,
          790,  790,  790,  790,  790,  790,  790,  790,  790,  852,
            0,  790,    0,  790,  790,  852,  852,  852,    0,    0,
            0,    0,    0,    0,    0,    0,  929,    0,    0,    0,
            0,    0,    0,    0,  852,    0,  929,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,  852,    0,    0,    0,
            0,    0,    0,    0,    0,    0,  817,  864,    0,    0,
          864,    0,  817,  817,  817,    0,    0,    0,  870,  764
    );

    protected array $actionDefault = array(
            3,32767,  102,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  100,32767,  619,  619,
          619,  619,32767,32767,  256,  102,32767,32767,  490,  407,
          407,  407,32767,32767,  563,  563,  563,  563,  563,32767,
        32767,32767,32767,32767,32767,  490,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,   36,    7,    8,   10,
           11,   49,   17,  329,  100,32767,32767,32767,32767,32767,
        32767,32767,32767,  102,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  394,  612,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,  494,  473,  474,  476,
          477,  406,  564,  618,  332,  615,  334,  405,  146,  344,
          335,  244,  260,  495,  261,  496,  499,  500,  217,  391,
          150,  151,  437,  491,  439,  489,  493,  438,  412,  418,
          419,  420,  421,  422,  423,  424,  425,  426,  427,  428,
          429,  430,  410,  411,  492,32767,32767,  470,  469,  468,
          435,32767,32767,32767,32767,32767,32767,32767,32767,  102,
        32767,  436,  440,  443,  409,  441,  442,  459,  460,  457,
          458,  461,32767,32767,  321,32767,32767,  462,  463,  464,
          465,  372,  196,  370,32767,32767,  111,  444,  321,  111,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,  450,
          451,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,  102,32767,
          100,  507,  557,  467,  445,  446,32767,  532,32767,  102,
        32767,  534,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,  559,  432,  434,  527,  613,  413,  616,32767,
          520,  100,  196,32767,  533,  196,  196,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,  558,32767,  626,
          520,  110,  110,  110,  110,  110,  110,  110,  110,  110,
          110,  110,  110,32767,  196,  110,32767,  110,  110,32767,
        32767,  100,  196,  196,  196,  196,  196,  196,  196,  196,
          535,  196,  196,  191,32767,  270,  272,  102,  581,  196,
          537,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,  394,32767,32767,32767,32767,  520,
          455,  139,32767,  522,  139,  565,  447,  448,  449,  565,
          565,  565,  317,  294,32767,32767,32767,32767,32767,  535,
          535,  100,  100,  100,  100,32767,32767,32767,32767,  111,
          506,   99,   99,   99,   99,   99,  103,  101,32767,32767,
        32767,32767,  225,32767,  101,   99,32767,  101,  101,32767,
        32767,  225,  227,  214,  229,32767,  585,  586,  225,  101,
          229,  229,  229,  249,  249,  509,  323,  101,   99,  101,
          101,  198,  323,  323,32767,  101,  509,  323,  509,  323,
          200,  323,  323,  323,  509,  323,32767,  101,  323,  216,
           99,   99,  323,32767,32767,32767,32767,  522,32767,32767,
        32767,32767,32767,32767,32767,  224,32767,32767,32767,32767,
        32767,32767,32767,32767,  552,32767,  570,  583,  453,  454,
          456,  569,  567,  478,  479,  480,  481,  482,  483,  484,
          486,  614,32767,  526,32767,32767,32767,  343,32767,  624,
        32767,32767,32767,    9,   74,  515,   42,   43,   51,   57,
          541,  542,  543,  544,  538,  539,  545,  540,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  625,32767,  565,32767,32767,32767,32767,
          452,  547,  591,32767,32767,  566,  617,32767,32767,32767,
        32767,32767,32767,32767,  139,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,  552,32767,  137,32767,32767,
        32767,32767,32767,32767,32767,32767,  548,32767,32767,32767,
          565,32767,32767,32767,32767,  319,  316,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  565,32767,32767,32767,32767,32767,  296,
        32767,  313,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
          390,  522,  299,  301,  302,32767,32767,32767,32767,  366,
        32767,32767,32767,32767,32767,32767,32767,32767,32767,32767,
        32767,32767,32767,  153,  153,    3,    3,  346,  153,  153,
          153,  346,  346,  153,  346,  346,  346,  153,  153,  153,
          153,  153,  153,  153,  282,  186,  264,  267,  249,  249,
          153,  358,  153,  392,  392,  401
    );

    protected array $goto = array(
          194,  194, 1056,  490,  708,  279,  276,  279,  279,  994,
          492,  551,  551,  911,  868,  911,  911,  551,  717,  551,
          551,  551,  551,  551,  551,  551,  551,  166,  166,  166,
          166,  218,  195,  191,  191,  176,  178,  213,  191,  191,
          191,  191,  191,  192,  192,  192,  192,  192,  186,  187,
          188,  189,  190,  215,  213,  216,  548,  549,  430,  550,
          553,  554,  555,  556,  557,  558,  559,  560, 1173,  167,
          168,  169,  193,  170,  171,  172,  164,  173,  174,  175,
          177,  212,  214,  217,  237,  240,  251,  252,  253,  255,
          256,  257,  258,  259,  260,  261,  267,  268,  269,  270,
          277,  289,  290,  314,  315,  436,  437,  438,  610,  219,
          220,  221,  222,  223,  224,  225,  226,  227,  228,  229,
          230,  231,  232,  233,  234,  186,  187,  188,  189,  190,
          215, 1173,  196,  197,  198,  199,  238,  179,  180,  200,
          181,  201,  197,  182,  239,  196,  163,  202,  203,  183,
          204,  205,  206,  184,  207,  208,  165,  209,  210,  211,
          185,  872,  563, 1087,  563,  563,  869, 1104, 1376, 1376,
          478,  478,  595,  473,  563,  612,  747,  649,  651,  478,
          351,  671, 1222, 1376,  870,  695,  698, 1029,  706,  715,
         1025,  722, 1059, 1059,  693,  971,  466,  844, 1051, 1067,
         1068,  988,  988,  988,  988, 1379, 1379,  466,  982,  989,
          356,  356,  356,  356,  927,  922,  923,  936,  878,  924,
          875,  925,  926,  876,  879, 1018,  930,  883,  990,  428,
          746,  882,  343,  564, 1027, 1022,  440,  673,  904, 1110,
         1106, 1107,  435,  337,  333,  334,  336,  605,  439,  338,
          441,  650,  602,  344,  343,  611, 1123,  349, 1277, 1056,
         1277, 1277,  633,  670,  459,  459,  720,  459,  459, 1056,
         1277,  519,  711, 1056, 1121, 1056, 1056, 1056, 1056, 1056,
         1056, 1056, 1056, 1056,  850,  523, 1056, 1056, 1056, 1056,
          666,  667, 1277,  684,  685,  686, 1366, 1277, 1277, 1277,
         1277,  359, 1015, 1277, 1277, 1277, 1358, 1358, 1358, 1358,
          672,  359,  359,  403,  406,  613,  617,  468, 1062, 1061,
          468,  944,  359,  359,  401,  945,  359,  600,  850, 1393,
          850,  960, 1170,  960,  622,  636,  639,  640,  641,  642,
          663,  664,  665,  719,  721,  567, 1253,  963,  359,  359,
         1080, 1254, 1257,  964,  865, 1258,  678,  865,  572,  565,
          931,  450,  932,  459,  459,  459,  459,  459,  459,  459,
          459,  459,  459,  459,  459,  635,  635,  459,  448,  459,
          459, 1308, 1308, 1308, 1308, 1308, 1308, 1308, 1308, 1308,
         1308, 1130, 1158, 1131,  694,  323,  565,  572,  597,  598,
          325,  608,  614, 1338,  629,  630,  865, 1327, 1327,  487,
         1351, 1352,   25, 1327, 1327, 1327, 1327, 1327, 1327, 1327,
         1327, 1327, 1327, 1065, 1066,  424,  561,  561,  561,  561,
         1349,  616, 1349, 1349,  352,  353,  339, 1324, 1324,  643,
          645,  647, 1349, 1324, 1324, 1324, 1324, 1324, 1324, 1324,
         1324, 1324, 1324,  566,  592,  566,  434,  567,  624,  566,
          862,  592,  886,  404,  472, 1360, 1360, 1360, 1360,  271,
          320,  628,  320,  320,  247,  247,  481,  609,  482,  483,
          898,  321,  307,  885,  896,  552,  552, 1384, 1385, 1345,
          891,  552,  552,  552,  552,  552,  552,  552,  552,  552,
          552,  245,  245,  245,  245,  242,  248, 1272,  977,  416,
          417,  705,  894,  410,  682,  846,  683, 1268,  421,  422,
          423, 1270,  696,  888,  511,  425,  512,  705, 1153,  347,
          705,  331,  518, 1037,    5,  618,    6,  865,  949, 1160,
         1095,  899,  887, 1092, 1096,  750, 1347, 1347, 1095, 1093,
         1034,  986,  419,  716,  997, 1353, 1354, 1047,  890,  488,
          676, 1013,  606,  627, 1273, 1274,  884, 1260,  442,  411,
          900,  999,  378,  860,    0, 1097,    0,  987, 1267, 1144,
         1260,    0,  442,    0,  755,  755,    0,    0, 1063, 1063,
            0,    0, 1275, 1335, 1336,  677, 1074, 1070, 1071, 1142,
          903,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0, 1032, 1032,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
            0,    0,    0,    0,    0,    0,    0,    0,  250,  250
    );

    protected array $gotoCheck = array(
           42,   42,   73,   84,   73,   23,   23,   23,   23,   49,
           84,  162,  162,   25,   25,   25,   25,  162,    9,  162,
          162,  162,  162,  162,  162,  162,  162,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   15,   19,  128,   19,   19,   26,   15,  188,  188,
          154,  154,   48,  156,   19,  131,   48,   48,   48,  154,
           97,   48,  156,  188,   27,   48,   48,   48,   48,   48,
           48,   48,   89,   89,   89,   89,   19,    6,   89,   89,
           89,   19,   19,   19,   19,  188,  188,   19,   19,   19,
           24,   24,   24,   24,   15,   15,   15,   15,   15,   15,
           15,   15,   15,   15,   15,   50,   15,   15,   50,   43,
           50,   15,  174,   50,   50,   50,   66,   66,   45,   15,
           15,   15,   66,   66,   66,   66,   66,   66,   66,   66,
           66,   66,  178,  174,  174,    8,    8,  185,   73,   73,
           73,   73,   56,   56,   23,   23,    8,   23,   23,   73,
           73,    8,    8,   73,    8,   73,   73,   73,   73,   73,
           73,   73,   73,   73,   12,   76,   73,   73,   73,   73,
           86,   86,   73,   86,   86,   86,  187,   73,   73,   73,
           73,   14,  103,   73,   73,   73,    9,    9,    9,    9,
           64,   14,   14,   59,   59,   59,   59,   83,  119,  119,
           83,   73,   14,   14,   62,   73,   14,  104,   12,   14,
           12,    9,  155,    9,   81,   81,   81,   81,   81,   81,
           81,   81,   81,   81,   81,   14,   79,   79,   14,   14,
          115,   79,   79,   79,   22,   79,  121,   22,   76,   76,
           65,   83,   65,   23,   23,   23,   23,   23,   23,   23,
           23,   23,   23,   23,   23,  108,  108,   23,  113,   23,
           23,  108,  108,  108,  108,  108,  108,  108,  108,  108,
          108,  146,  146,  146,  117,   76,   76,   76,   76,   76,
           76,   76,   76,   14,   76,   76,   22,  176,  176,  182,
          182,  182,   76,  176,  176,  176,  176,  176,  176,  176,
          176,  176,  176,  120,  120,   14,  107,  107,  107,  107,
          131,  107,  131,  131,   97,   97,   29,  177,  177,   85,
           85,   85,  131,  177,  177,  177,  177,  177,  177,  177,
          177,  177,  177,    9,    9,    9,   13,   14,   13,    9,
           18,    9,   35,    9,    9,  131,  131,  131,  131,   24,
           24,   80,   24,   24,    5,    5,    9,    9,    9,    9,
           35,  175,  175,   35,    9,  179,  179,    9,    9,  131,
           39,  179,  179,  179,  179,  179,  179,  179,  179,  179,
          179,    5,    5,    5,    5,    5,    5,   20,   92,   82,
           82,    7,    9,   28,   82,    7,   82,  166,   82,   82,
           82,   14,   82,   37,  160,   82,  160,    7,  153,   82,
            7,    9,  160,  110,   46,   17,   46,   22,   17,   17,
          131,   16,   16,   16,   16,   99,  131,  131,  131,  130,
           17,   93,   93,   93,   16,  184,  184,  114,   17,  157,
           17,   17,    2,    2,   20,   20,   17,   20,  118,   31,
           41,   96,  138,   20,   -1,  133,   -1,   16,   17,  149,
           20,   -1,  118,   -1,   24,   24,   -1,   -1,  118,  118,
           -1,   -1,   20,   20,   20,  118,  118,  118,  118,   16,
           16,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,  107,  107,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
           -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,    5,    5
    );

    protected array $gotoBase = array(
            0,    0, -163,    0,    0,  473,  187,  504,  247,    8,
            0,    0,  -11,  117,    5, -187,   79,   61,  152, -101,
          107,    0,   78,    2,  207,   10,  162,  180,  174,  141,
            0,  122,    0,    0,    0,   86,    0,  182,    0,  171,
            0,  119,   -1,  206,    0,  212, -216,    0, -552,   -9,
          213,    0,    0,    0,    0,    0,  222,    0,    0,  268,
            0,    0,  282,    0,   74,  346,    1,    0,    0,    0,
            0,    0,    0,   -5,    0,    0,   13,    0,    0,  -70,
          146,  -28,    7,   41, -478,  -51, -441,    0,    0,  -88,
            0,    0,  181,  248,    0,    0,  118, -314,    0,  130,
            0,    0,    0,  267,  284,    0,    0,  398,  140,    0,
          158,    0,    0,  100,  133,   76,    0,  112,  304,   38,
          139,   65,    0,    0,    0,    0,    0,    0,  161,    0,
          168,  167,    0,  123,    0,    0,    0,    0, -182,    0,
            0,    0,    0,    0,    0,    0,  120,    0,    0,  125,
            0,    0,    0,  173,  136,   90,  -93,  109,    0,    0,
           18,    0, -224,    0,    0,    0,  143,    0,    0,    0,
            0,    0,    0,    0,  -64,  164,  172,  202,  223,  250,
            0,    0,  110,    0,  176,  227,    0,  265, -138,    0,
            0
    );

    protected array $gotoDefault = array(
        -32768,  524,  757,    4,  758,  953,  833,  842,  588,  542,
          718,  348,  637,  431, 1343,  929, 1159,  607,  861, 1286,
         1292,  467,  864,  328,  744,  941,  912,  913,  407,  394,
          877,  405,  661,  638,  505,  897,  463,  889,  497,  892,
          462,  901,  162,  427,  521,  905,    3,  908,  570,  939,
          992,  395,  916,  396,  689,  918,  591,  920,  921,  402,
          408,  409, 1164,  599,  634,  933,  254,  593,  934,  393,
          935,  943,  398,  400,  699,  477,  516,  510,  420, 1125,
          594,  621,  658,  456,  484,  632,  644,  631,  491,  443,
          426,  327,  976,  984,  498,  475,  998,  350, 1006,  752,
         1172,  652,  500, 1014,  653, 1021, 1024,  543,  544,  489,
         1036,  264, 1039,  501, 1048,   23,  679, 1053, 1054,  680,
          654, 1076,  655,  681,  656, 1078,  474,  589, 1086,  464,
         1094, 1332,  465, 1098,  262, 1101,  278,  354,  377,  444,
         1108, 1109,    9, 1115,  709,  710,   19,  273,  520, 1143,
          700, 1149,  272, 1152,  461, 1171,  460, 1241, 1243,  571,
          502, 1261,  311, 1264,  692,  517, 1269,  457, 1334,  458,
          545,  485,  335,  546, 1377,  306,  357,  332,  562,  312,
          358,  547,  486, 1340, 1348,  329,   31, 1367, 1378,  604,
          626
    );

    protected array $ruleToNonTerminal = array(
            0,    1,    3,    3,    2,    5,    5,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    6,    6,    6,
            6,    6,    6,    6,    6,    6,    6,    7,    7,    7,
            7,    7,    7,    7,    7,    8,    8,    9,   10,   11,
           11,   11,   12,   12,   13,   13,   14,   15,   15,   16,
           16,   17,   17,   18,   18,   21,   21,   22,   23,   23,
           24,   24,    4,    4,    4,    4,    4,    4,    4,    4,
            4,    4,    4,    4,   29,   29,   30,   30,   32,   34,
           34,   28,   36,   36,   33,   38,   38,   35,   35,   37,
           37,   39,   39,   31,   40,   40,   41,   43,   44,   44,
           45,   45,   46,   46,   48,   47,   47,   47,   47,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   49,   49,   49,   49,   49,   49,   49,
           49,   49,   49,   25,   25,   50,   69,   69,   72,   72,
           71,   70,   70,   63,   75,   75,   76,   76,   77,   77,
           78,   78,   79,   79,   80,   80,   80,   26,   26,   27,
           27,   27,   27,   27,   88,   88,   90,   90,   83,   83,
           91,   91,   92,   92,   92,   84,   84,   87,   87,   85,
           85,   93,   94,   94,   57,   57,   65,   65,   68,   68,
           68,   67,   95,   95,   96,   58,   58,   58,   58,   97,
           97,   98,   98,   99,   99,  100,  101,  101,  102,  102,
          103,  103,   55,   55,   51,   51,  105,   53,   53,  106,
           52,   52,   54,   54,   64,   64,   64,   64,   81,   81,
          109,  109,  111,  111,  112,  112,  112,  112,  112,  112,
          112,  110,  110,  110,  115,  115,  115,  115,   89,   89,
          118,  118,  118,  119,  119,  116,  116,  120,  120,  122,
          122,  123,  123,  117,  124,  124,  121,  125,  125,  125,
          125,  113,  113,   82,   82,   82,   20,   20,   20,  127,
          126,  126,  128,  128,  128,  128,   60,  129,  129,  130,
           61,  132,  132,  133,  133,  134,  134,   86,  135,  135,
          135,  135,  135,  135,  135,  135,  141,  141,  142,  142,
          143,  143,  143,  143,  143,  144,  145,  145,  140,  140,
          136,  136,  139,  139,  147,  147,  146,  146,  146,  146,
          146,  146,  146,  146,  146,  146,  137,  148,  148,  150,
          149,  149,  138,  138,  114,  114,  151,  151,  153,  153,
          153,  152,  152,   62,  104,  154,  154,   56,   56,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,   42,   42,   42,   42,   42,   42,   42,
           42,   42,   42,  161,  162,  162,  163,  155,  155,  160,
          160,  164,  165,  165,  166,  167,  168,  168,  168,  168,
           19,   19,   73,   73,   73,   73,  156,  156,  156,  156,
          170,  170,  159,  159,  159,  157,  157,  176,  176,  176,
          176,  176,  176,  176,  176,  176,  176,  177,  177,  177,
          108,  179,  179,  179,  179,  158,  158,  158,  158,  158,
          158,  158,  158,   59,   59,  173,  173,  173,  173,  173,
          180,  180,  169,  169,  169,  169,  181,  181,  181,  181,
          181,   74,   74,   66,   66,   66,   66,  131,  131,  131,
          131,  184,  183,  172,  172,  172,  172,  172,  172,  171,
          171,  171,  182,  182,  182,  182,  107,  178,  186,  186,
          185,  185,  187,  187,  187,  187,  187,  187,  187,  187,
          175,  175,  175,  175,  174,  189,  188,  188,  188,  188,
          188,  188,  188,  188,  190,  190,  190,  190
    );

    protected array $ruleToLength = array(
            1,    1,    2,    0,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    1,    1,    0,
            1,    0,    1,    1,    2,    1,    3,    4,    1,    2,
            0,    1,    1,    1,    1,    4,    3,    5,    4,    3,
            4,    1,    3,    4,    1,    1,    8,    7,    2,    3,
            1,    2,    3,    1,    2,    3,    1,    1,    3,    1,
            3,    1,    2,    2,    3,    1,    3,    2,    3,    1,
            3,    3,    2,    0,    1,    1,    1,    1,    1,    3,
            7,   10,    5,    7,    9,    5,    3,    3,    3,    3,
            3,    3,    1,    2,    5,    7,    9,    6,    5,    6,
            3,    2,    1,    1,    1,    1,    0,    2,    1,    3,
            8,    0,    4,    2,    1,    3,    0,    1,    0,    1,
            0,    1,    3,    1,    1,    1,    1,    8,    9,    7,
            8,    7,    6,    8,    0,    2,    0,    2,    1,    2,
            1,    2,    1,    1,    1,    0,    2,    0,    2,    0,
            2,    2,    1,    3,    1,    4,    1,    4,    1,    1,
            4,    2,    1,    3,    3,    3,    4,    4,    5,    0,
            2,    4,    3,    1,    1,    7,    0,    2,    1,    3,
            3,    4,    1,    4,    0,    2,    5,    0,    2,    6,
            0,    2,    0,    3,    1,    2,    1,    1,    2,    0,
            1,    3,    0,    2,    1,    1,    1,    1,    1,    1,
            1,    7,    9,    6,    1,    2,    1,    1,    1,    1,
            1,    1,    1,    1,    3,    3,    3,    1,    3,    3,
            3,    3,    3,    1,    3,    3,    1,    1,    2,    1,
            1,    0,    1,    0,    2,    2,    2,    4,    3,    1,
            1,    3,    1,    2,    2,    3,    2,    3,    1,    1,
            2,    3,    1,    1,    3,    2,    0,    1,    5,    7,
            5,    6,   10,    3,    5,    1,    1,    3,    0,    2,
            4,    5,    4,    4,    4,    3,    1,    1,    1,    1,
            1,    1,    0,    1,    1,    2,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    2,    1,    3,    1,
            1,    3,    0,    2,    0,    3,    5,    8,    1,    3,
            3,    0,    2,    2,    2,    3,    1,    0,    1,    1,
            3,    3,    3,    4,    4,    1,    1,    2,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    2,    2,    2,    2,    3,    3,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    3,    3,
            3,    3,    3,    2,    2,    2,    2,    3,    3,    3,
            3,    3,    3,    3,    3,    3,    3,    3,    5,    4,
            3,    4,    4,    2,    2,    4,    2,    2,    2,    2,
            2,    2,    2,    2,    2,    2,    2,    1,    3,    2,
            1,    2,    4,    2,    2,    8,    9,    8,    9,    9,
           10,    9,   10,    8,    3,    2,    2,    1,    1,    0,
            4,    2,    1,    3,    2,    1,    2,    2,    2,    4,
            1,    1,    1,    1,    1,    1,    1,    1,    3,    1,
            1,    1,    0,    1,    1,    0,    1,    1,    1,    1,
            1,    1,    1,    1,    1,    1,    1,    3,    5,    3,
            3,    4,    1,    1,    3,    1,    1,    1,    1,    1,
            3,    2,    3,    0,    1,    1,    3,    1,    1,    1,
            1,    1,    1,    3,    1,    1,    1,    4,    1,    4,
            4,    0,    1,    1,    1,    3,    3,    1,    4,    2,
            2,    1,    3,    1,    4,    3,    3,    3,    3,    1,
            3,    1,    1,    3,    1,    1,    4,    1,    1,    1,
            3,    1,    1,    2,    1,    3,    4,    3,    2,    0,
            2,    2,    1,    2,    1,    1,    1,    4,    3,    3,
            3,    3,    6,    3,    1,    1,    2,    1
    );

    protected function initReduceCallbacks(): void {
        $this->reduceCallbacks = [
            0 => null,
            1 => static function ($self, $stackPos) {
                 $self->semValue = $self->handleNamespaces($self->semStack[$stackPos-(1-1)]);
            },
            2 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; } $self->semValue = $self->semStack[$stackPos-(2-1)];;
            },
            3 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            4 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            5 => null,
            6 => null,
            7 => null,
            8 => null,
            9 => null,
            10 => null,
            11 => null,
            12 => null,
            13 => null,
            14 => null,
            15 => null,
            16 => null,
            17 => null,
            18 => null,
            19 => null,
            20 => null,
            21 => null,
            22 => null,
            23 => null,
            24 => null,
            25 => null,
            26 => null,
            27 => null,
            28 => null,
            29 => null,
            30 => null,
            31 => null,
            32 => null,
            33 => null,
            34 => null,
            35 => null,
            36 => null,
            37 => null,
            38 => null,
            39 => null,
            40 => null,
            41 => null,
            42 => null,
            43 => null,
            44 => null,
            45 => null,
            46 => null,
            47 => null,
            48 => null,
            49 => null,
            50 => null,
            51 => null,
            52 => null,
            53 => null,
            54 => null,
            55 => null,
            56 => null,
            57 => null,
            58 => null,
            59 => null,
            60 => null,
            61 => null,
            62 => null,
            63 => null,
            64 => null,
            65 => null,
            66 => null,
            67 => null,
            68 => null,
            69 => null,
            70 => null,
            71 => null,
            72 => null,
            73 => null,
            74 => null,
            75 => null,
            76 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; if ($self->semValue === "<?=") $self->emitError(new Error('Cannot use "<?=" as an identifier', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])));
            },
            77 => null,
            78 => null,
            79 => null,
            80 => null,
            81 => null,
            82 => null,
            83 => null,
            84 => null,
            85 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            86 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            87 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            88 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            89 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            90 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            91 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            92 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            93 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            94 => null,
            95 => static function ($self, $stackPos) {
                 $self->semValue = new Name(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            96 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            97 => static function ($self, $stackPos) {
                 /* nothing */
            },
            98 => static function ($self, $stackPos) {
                 /* nothing */
            },
            99 => static function ($self, $stackPos) {
                 /* nothing */
            },
            100 => static function ($self, $stackPos) {
                 $self->emitError(new Error('A trailing comma is not allowed here', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])));
            },
            101 => null,
            102 => null,
            103 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Attribute($self->semStack[$stackPos-(1-1)], [], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            104 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Attribute($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            105 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            106 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            107 => static function ($self, $stackPos) {
                 $self->semValue = new Node\AttributeGroup($self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            108 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            109 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            110 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            111 => null,
            112 => null,
            113 => null,
            114 => null,
            115 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\HaltCompiler($self->handleHaltCompiler(), $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            116 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_($self->semStack[$stackPos-(3-2)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_SEMICOLON);
            $self->checkNamespace($self->semValue);
            },
            117 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_($self->semStack[$stackPos-(5-2)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $self->checkNamespace($self->semValue);
            },
            118 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Namespace_(null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            $self->semValue->setAttribute('kind', Stmt\Namespace_::KIND_BRACED);
            $self->checkNamespace($self->semValue);
            },
            119 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Use_($self->semStack[$stackPos-(3-2)], Stmt\Use_::TYPE_NORMAL, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            120 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Use_($self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            121 => null,
            122 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Const_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), []);
            },
            123 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Const_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(4-1)]);
            $self->checkConstantAttributes($self->semValue);
            },
            124 => static function ($self, $stackPos) {
                 $self->semValue = Stmt\Use_::TYPE_FUNCTION;
            },
            125 => static function ($self, $stackPos) {
                 $self->semValue = Stmt\Use_::TYPE_CONSTANT;
            },
            126 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\GroupUse($self->semStack[$stackPos-(8-3)], $self->semStack[$stackPos-(8-6)], $self->semStack[$stackPos-(8-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            127 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\GroupUse($self->semStack[$stackPos-(7-2)], $self->semStack[$stackPos-(7-5)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            128 => null,
            129 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            130 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            131 => null,
            132 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            133 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            134 => null,
            135 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            136 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            137 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(1-1));
            },
            138 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(3-3));
            },
            139 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(1-1)], null, Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(1-1));
            },
            140 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UseItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], Stmt\Use_::TYPE_UNKNOWN, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->checkUseUse($self->semValue, $stackPos-(3-3));
            },
            141 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $self->semValue->type = Stmt\Use_::TYPE_NORMAL;
            },
            142 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)]; $self->semValue->type = $self->semStack[$stackPos-(2-1)];
            },
            143 => null,
            144 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            145 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            146 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            147 => null,
            148 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            149 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            150 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_(new Node\Identifier($self->semStack[$stackPos-(3-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)],  $self->tokenEndStack[$stackPos-(3-1)])), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            151 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Const_(new Node\Identifier($self->semStack[$stackPos-(3-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)],  $self->tokenEndStack[$stackPos-(3-1)])), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            152 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; } $self->semValue = $self->semStack[$stackPos-(2-1)];;
            },
            153 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            154 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            155 => null,
            156 => null,
            157 => null,
            158 => static function ($self, $stackPos) {
                 throw new Error('__HALT_COMPILER() can only be used from the outermost scope', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            159 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Block($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            160 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\If_($self->semStack[$stackPos-(7-3)], ['stmts' => $self->semStack[$stackPos-(7-5)], 'elseifs' => $self->semStack[$stackPos-(7-6)], 'else' => $self->semStack[$stackPos-(7-7)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            161 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\If_($self->semStack[$stackPos-(10-3)], ['stmts' => $self->semStack[$stackPos-(10-6)], 'elseifs' => $self->semStack[$stackPos-(10-7)], 'else' => $self->semStack[$stackPos-(10-8)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            162 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\While_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            163 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Do_($self->semStack[$stackPos-(7-5)], $self->semStack[$stackPos-(7-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            164 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\For_(['init' => $self->semStack[$stackPos-(9-3)], 'cond' => $self->semStack[$stackPos-(9-5)], 'loop' => $self->semStack[$stackPos-(9-7)], 'stmts' => $self->semStack[$stackPos-(9-9)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            165 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Switch_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            166 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Break_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            167 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Continue_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            168 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Return_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            169 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Global_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            170 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Static_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            171 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Echo_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            172 => static function ($self, $stackPos) {

        $self->semValue = new Stmt\InlineHTML($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
        $self->semValue->setAttribute('hasLeadingNewline', $self->inlineHtmlHasLeadingNewline($stackPos-(1-1)));

            },
            173 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Expression($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            174 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Unset_($self->semStack[$stackPos-(5-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            175 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-5)][0], ['keyVar' => null, 'byRef' => $self->semStack[$stackPos-(7-5)][1], 'stmts' => $self->semStack[$stackPos-(7-7)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            176 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(9-3)], $self->semStack[$stackPos-(9-7)][0], ['keyVar' => $self->semStack[$stackPos-(9-5)], 'byRef' => $self->semStack[$stackPos-(9-7)][1], 'stmts' => $self->semStack[$stackPos-(9-9)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            177 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Foreach_($self->semStack[$stackPos-(6-3)], new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(6-4)],  $self->tokenEndStack[$stackPos-(6-4)])), ['stmts' => $self->semStack[$stackPos-(6-6)]], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            178 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Declare_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            179 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TryCatch($self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-5)], $self->semStack[$stackPos-(6-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])); $self->checkTryCatch($self->semValue);
            },
            180 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Goto_($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            181 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Label($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            182 => static function ($self, $stackPos) {
                 $self->semValue = null; /* means: no statement */
            },
            183 => null,
            184 => static function ($self, $stackPos) {
                 $self->semValue = $self->maybeCreateNop($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]);
            },
            185 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $self->semValue = $self->semStack[$stackPos-(1-1)]->stmts; } else if ($self->semStack[$stackPos-(1-1)] === null) { $self->semValue = []; } else { $self->semValue = [$self->semStack[$stackPos-(1-1)]]; };
            },
            186 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            187 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            188 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            189 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            190 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Catch_($self->semStack[$stackPos-(8-3)], $self->semStack[$stackPos-(8-4)], $self->semStack[$stackPos-(8-7)], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            191 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            192 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Finally_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            193 => null,
            194 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            195 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            196 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            197 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            198 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            199 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            200 => static function ($self, $stackPos) {
                 $self->semValue = false;
            },
            201 => static function ($self, $stackPos) {
                 $self->semValue = true;
            },
            202 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            203 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            204 => null,
            205 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            206 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            207 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Function_($self->semStack[$stackPos-(8-3)], ['byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-5)], 'returnType' => $self->semStack[$stackPos-(8-7)], 'stmts' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            208 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Function_($self->semStack[$stackPos-(9-4)], ['byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-6)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            209 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Class_($self->semStack[$stackPos-(7-2)], ['type' => $self->semStack[$stackPos-(7-1)], 'extends' => $self->semStack[$stackPos-(7-3)], 'implements' => $self->semStack[$stackPos-(7-4)], 'stmts' => $self->semStack[$stackPos-(7-6)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClass($self->semValue, $stackPos-(7-2));
            },
            210 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Class_($self->semStack[$stackPos-(8-3)], ['type' => $self->semStack[$stackPos-(8-2)], 'extends' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClass($self->semValue, $stackPos-(8-3));
            },
            211 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Interface_($self->semStack[$stackPos-(7-3)], ['extends' => $self->semStack[$stackPos-(7-4)], 'stmts' => $self->semStack[$stackPos-(7-6)], 'attrGroups' => $self->semStack[$stackPos-(7-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            $self->checkInterface($self->semValue, $stackPos-(7-3));
            },
            212 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Trait_($self->semStack[$stackPos-(6-3)], ['stmts' => $self->semStack[$stackPos-(6-5)], 'attrGroups' => $self->semStack[$stackPos-(6-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            213 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Enum_($self->semStack[$stackPos-(8-3)], ['scalarType' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkEnum($self->semValue, $stackPos-(8-3));
            },
            214 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            215 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            216 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            217 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            218 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            219 => null,
            220 => null,
            221 => static function ($self, $stackPos) {
                 $self->checkClassModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            222 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::ABSTRACT;
            },
            223 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::FINAL;
            },
            224 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            225 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            226 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            227 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            228 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            229 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            230 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            231 => null,
            232 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            233 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            234 => null,
            235 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            236 => null,
            237 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            238 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(1-1)] instanceof Stmt\Block) { $self->semValue = $self->semStack[$stackPos-(1-1)]->stmts; } else if ($self->semStack[$stackPos-(1-1)] === null) { $self->semValue = []; } else { $self->semValue = [$self->semStack[$stackPos-(1-1)]]; };
            },
            239 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            240 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            241 => null,
            242 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            243 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            244 => static function ($self, $stackPos) {
                 $self->semValue = new Node\DeclareItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            245 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            246 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-3)];
            },
            247 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            248 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(5-3)];
            },
            249 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            250 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            251 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Case_($self->semStack[$stackPos-(4-2)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            252 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Case_(null, $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            253 => null,
            254 => null,
            255 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Match_($self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]));
            },
            256 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            257 => null,
            258 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            259 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            260 => static function ($self, $stackPos) {
                 $self->semValue = new Node\MatchArm($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            261 => static function ($self, $stackPos) {
                 $self->semValue = new Node\MatchArm(null, $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            262 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            263 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            264 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            265 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            266 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ElseIf_($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            267 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            268 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            269 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ElseIf_($self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-6)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])); $self->fixupAlternativeElse($self->semValue);
            },
            270 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            271 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Else_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            272 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            273 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Else_($self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->fixupAlternativeElse($self->semValue);
            },
            274 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)], false);
            },
            275 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(2-2)], true);
            },
            276 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)], false);
            },
            277 => static function ($self, $stackPos) {
                 $self->semValue = array($self->fixupArrayDestructuring($self->semStack[$stackPos-(1-1)]), false);
            },
            278 => null,
            279 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            280 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            281 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            282 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            283 => static function ($self, $stackPos) {
                 $self->checkModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            284 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC;
            },
            285 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED;
            },
            286 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE;
            },
            287 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC_SET;
            },
            288 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED_SET;
            },
            289 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE_SET;
            },
            290 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            291 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param($self->semStack[$stackPos-(7-6)], null, $self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-4)], $self->semStack[$stackPos-(7-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(7-2)], $self->semStack[$stackPos-(7-1)], $self->semStack[$stackPos-(7-7)]);
            $self->checkParam($self->semValue);
            $self->addPropertyNameToHooks($self->semValue);
            },
            292 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param($self->semStack[$stackPos-(9-6)], $self->semStack[$stackPos-(9-8)], $self->semStack[$stackPos-(9-3)], $self->semStack[$stackPos-(9-4)], $self->semStack[$stackPos-(9-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(9-2)], $self->semStack[$stackPos-(9-1)], $self->semStack[$stackPos-(9-9)]);
            $self->checkParam($self->semValue);
            $self->addPropertyNameToHooks($self->semValue);
            },
            293 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Param(new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos])), null, $self->semStack[$stackPos-(6-3)], $self->semStack[$stackPos-(6-4)], $self->semStack[$stackPos-(6-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(6-2)], $self->semStack[$stackPos-(6-1)]);
            },
            294 => null,
            295 => static function ($self, $stackPos) {
                 $self->semValue = new Node\NullableType($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            296 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UnionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            297 => null,
            298 => null,
            299 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Name('static', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            300 => static function ($self, $stackPos) {
                 $self->semValue = $self->handleBuiltinTypes($self->semStack[$stackPos-(1-1)]);
            },
            301 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier('array', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            302 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Identifier('callable', $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            303 => null,
            304 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            305 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            306 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            307 => null,
            308 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            309 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            310 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            311 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            312 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            313 => static function ($self, $stackPos) {
                 $self->semValue = new Node\IntersectionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            314 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            315 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            316 => static function ($self, $stackPos) {
                 $self->semValue = new Node\IntersectionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            317 => null,
            318 => static function ($self, $stackPos) {
                 $self->semValue = new Node\NullableType($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            319 => static function ($self, $stackPos) {
                 $self->semValue = new Node\UnionType($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            320 => null,
            321 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            322 => null,
            323 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            324 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(2-2)];
            },
            325 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            326 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            327 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-2)];
            },
            328 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-2)]);
            },
            329 => static function ($self, $stackPos) {
                 $self->semValue = new Node\VariadicPlaceholder($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            330 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            331 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            332 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(1-1)], false, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            333 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(2-2)], true, false, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            334 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(2-2)], false, true, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            335 => static function ($self, $stackPos) {
                 $self->semValue = new Node\Arg($self->semStack[$stackPos-(3-3)], false, false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(3-1)]);
            },
            336 => null,
            337 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            338 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            339 => null,
            340 => null,
            341 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            342 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            343 => static function ($self, $stackPos) {
                 $self->semValue = new Node\StaticVar($self->semStack[$stackPos-(1-1)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            344 => static function ($self, $stackPos) {
                 $self->semValue = new Node\StaticVar($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            345 => static function ($self, $stackPos) {
                 if ($self->semStack[$stackPos-(2-2)] !== null) { $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)]; } else { $self->semValue = $self->semStack[$stackPos-(2-1)]; }
            },
            346 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            347 => static function ($self, $stackPos) {
                 $nop = $self->maybeCreateZeroLengthNop($self->tokenPos);;
            if ($nop !== null) { $self->semStack[$stackPos-(1-1)][] = $nop; } $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            348 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Property($self->semStack[$stackPos-(5-2)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-1)]);
            },
            349 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\Property($self->semStack[$stackPos-(7-2)], $self->semStack[$stackPos-(7-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(7-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(7-3)], $self->semStack[$stackPos-(7-1)], $self->semStack[$stackPos-(7-6)]);
            $self->checkPropertyHooksForMultiProperty($self->semValue, $stackPos-(7-5));
            $self->checkEmptyPropertyHookList($self->semStack[$stackPos-(7-6)], $stackPos-(7-5));
            $self->addPropertyNameToHooks($self->semValue);
            },
            350 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassConst($self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(5-1)]);
            $self->checkClassConst($self->semValue, $stackPos-(5-2));
            },
            351 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassConst($self->semStack[$stackPos-(6-5)], $self->semStack[$stackPos-(6-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]), $self->semStack[$stackPos-(6-1)], $self->semStack[$stackPos-(6-4)]);
            $self->checkClassConst($self->semValue, $stackPos-(6-2));
            },
            352 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\ClassMethod($self->semStack[$stackPos-(10-5)], ['type' => $self->semStack[$stackPos-(10-2)], 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-7)], 'returnType' => $self->semStack[$stackPos-(10-9)], 'stmts' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            $self->checkClassMethod($self->semValue, $stackPos-(10-2));
            },
            353 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUse($self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            354 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\EnumCase($self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            355 => static function ($self, $stackPos) {
                 $self->semValue = null; /* will be skipped */
            },
            356 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            357 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            358 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            359 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            360 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Precedence($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            361 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(5-1)][0], $self->semStack[$stackPos-(5-1)][1], $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            362 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], $self->semStack[$stackPos-(4-3)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            363 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            364 => static function ($self, $stackPos) {
                 $self->semValue = new Stmt\TraitUseAdaptation\Alias($self->semStack[$stackPos-(4-1)][0], $self->semStack[$stackPos-(4-1)][1], null, $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            365 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)]);
            },
            366 => null,
            367 => static function ($self, $stackPos) {
                 $self->semValue = array(null, $self->semStack[$stackPos-(1-1)]);
            },
            368 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            369 => null,
            370 => null,
            371 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            372 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            373 => null,
            374 => null,
            375 => static function ($self, $stackPos) {
                 $self->checkModifier($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            376 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC;
            },
            377 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED;
            },
            378 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE;
            },
            379 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PUBLIC_SET;
            },
            380 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PROTECTED_SET;
            },
            381 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::PRIVATE_SET;
            },
            382 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::STATIC;
            },
            383 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::ABSTRACT;
            },
            384 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::FINAL;
            },
            385 => static function ($self, $stackPos) {
                 $self->semValue = Modifiers::READONLY;
            },
            386 => null,
            387 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            388 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            389 => static function ($self, $stackPos) {
                 $self->semValue = new Node\VarLikeIdentifier(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            390 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyItem($self->semStack[$stackPos-(1-1)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            391 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyItem($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            392 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            393 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            394 => static function ($self, $stackPos) {
                 $self->semValue = [];
            },
            395 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)]; $self->checkEmptyPropertyHookList($self->semStack[$stackPos-(3-2)], $stackPos-(3-1));
            },
            396 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyHook($self->semStack[$stackPos-(5-4)], $self->semStack[$stackPos-(5-5)], ['flags' => $self->semStack[$stackPos-(5-2)], 'byRef' => $self->semStack[$stackPos-(5-3)], 'params' => [], 'attrGroups' => $self->semStack[$stackPos-(5-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            $self->checkPropertyHook($self->semValue, null);
            },
            397 => static function ($self, $stackPos) {
                 $self->semValue = new Node\PropertyHook($self->semStack[$stackPos-(8-4)], $self->semStack[$stackPos-(8-8)], ['flags' => $self->semStack[$stackPos-(8-2)], 'byRef' => $self->semStack[$stackPos-(8-3)], 'params' => $self->semStack[$stackPos-(8-6)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            $self->checkPropertyHook($self->semValue, $stackPos-(8-5));
            },
            398 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            399 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            400 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            401 => static function ($self, $stackPos) {
                 $self->semValue = 0;
            },
            402 => static function ($self, $stackPos) {
                 $self->checkPropertyHookModifiers($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $stackPos-(2-2)); $self->semValue = $self->semStack[$stackPos-(2-1)] | $self->semStack[$stackPos-(2-2)];
            },
            403 => null,
            404 => null,
            405 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            406 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            407 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            408 => null,
            409 => null,
            410 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            411 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->fixupArrayDestructuring($self->semStack[$stackPos-(3-1)]), $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            412 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Assign($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            413 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignRef($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            414 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignRef($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            if (!$self->phpVersion->allowsAssignNewByReference()) {
                $self->emitError(new Error('Cannot assign new by reference', $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos])));
            }

            },
            415 => null,
            416 => null,
            417 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Clone_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            418 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Plus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            419 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Minus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            420 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Mul($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            421 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Div($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            422 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Concat($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            423 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Mod($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            424 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            425 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            426 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\BitwiseXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            427 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\ShiftLeft($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            428 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\ShiftRight($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            429 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Pow($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            430 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\AssignOp\Coalesce($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            431 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PostInc($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            432 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PreInc($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            433 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PostDec($self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            434 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PreDec($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            435 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BooleanOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            436 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BooleanAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            437 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            438 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            439 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\LogicalXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            440 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseOr($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            441 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            442 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseAnd($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            443 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\BitwiseXor($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            444 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Concat($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            445 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Plus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            446 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Minus($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            447 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Mul($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            448 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Div($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            449 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Mod($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            450 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\ShiftLeft($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            451 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\ShiftRight($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            452 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Pow($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            453 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\UnaryPlus($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            454 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\UnaryMinus($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            455 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BooleanNot($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            456 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BitwiseNot($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            457 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Identical($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            458 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\NotIdentical($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            459 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Equal($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            460 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\NotEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            461 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Spaceship($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            462 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Smaller($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            463 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\SmallerOrEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            464 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Greater($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            465 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\GreaterOrEqual($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            466 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Instanceof_($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            467 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            468 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Ternary($self->semStack[$stackPos-(5-1)], $self->semStack[$stackPos-(5-3)], $self->semStack[$stackPos-(5-5)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            469 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Ternary($self->semStack[$stackPos-(4-1)], null, $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            470 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\BinaryOp\Coalesce($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            471 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Isset_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            472 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Empty_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            473 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            474 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_INCLUDE_ONCE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            475 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Eval_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            476 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            477 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Include_($self->semStack[$stackPos-(2-2)], Expr\Include_::TYPE_REQUIRE_ONCE, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            478 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Int_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            479 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]);
            $attrs['kind'] = $self->getFloatCastKind($self->semStack[$stackPos-(2-1)]);
            $self->semValue = new Expr\Cast\Double($self->semStack[$stackPos-(2-2)], $attrs);
            },
            480 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\String_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            481 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Array_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            482 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Object_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            483 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Bool_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            484 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Cast\Unset_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            485 => static function ($self, $stackPos) {
                 $self->semValue = $self->createExitExpr($self->semStack[$stackPos-(2-1)], $stackPos-(2-1), $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            486 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ErrorSuppress($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            487 => null,
            488 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ShellExec($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            489 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Print_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            490 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_(null, null, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            491 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_($self->semStack[$stackPos-(2-2)], null, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            492 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Yield_($self->semStack[$stackPos-(4-4)], $self->semStack[$stackPos-(4-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            493 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\YieldFrom($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            494 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Throw_($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            495 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-4)], 'returnType' => $self->semStack[$stackPos-(8-6)], 'expr' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            496 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'returnType' => $self->semStack[$stackPos-(9-7)], 'expr' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            497 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => false, 'byRef' => $self->semStack[$stackPos-(8-2)], 'params' => $self->semStack[$stackPos-(8-4)], 'uses' => $self->semStack[$stackPos-(8-6)], 'returnType' => $self->semStack[$stackPos-(8-7)], 'stmts' => $self->semStack[$stackPos-(8-8)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos]));
            },
            498 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => true, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'uses' => $self->semStack[$stackPos-(9-7)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => []], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            499 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => false, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'returnType' => $self->semStack[$stackPos-(9-7)], 'expr' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            500 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrowFunction(['static' => true, 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-6)], 'returnType' => $self->semStack[$stackPos-(10-8)], 'expr' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            501 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => false, 'byRef' => $self->semStack[$stackPos-(9-3)], 'params' => $self->semStack[$stackPos-(9-5)], 'uses' => $self->semStack[$stackPos-(9-7)], 'returnType' => $self->semStack[$stackPos-(9-8)], 'stmts' => $self->semStack[$stackPos-(9-9)], 'attrGroups' => $self->semStack[$stackPos-(9-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(9-1)], $self->tokenEndStack[$stackPos]));
            },
            502 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Closure(['static' => true, 'byRef' => $self->semStack[$stackPos-(10-4)], 'params' => $self->semStack[$stackPos-(10-6)], 'uses' => $self->semStack[$stackPos-(10-8)], 'returnType' => $self->semStack[$stackPos-(10-9)], 'stmts' => $self->semStack[$stackPos-(10-10)], 'attrGroups' => $self->semStack[$stackPos-(10-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(10-1)], $self->tokenEndStack[$stackPos]));
            },
            503 => static function ($self, $stackPos) {
                 $self->semValue = array(new Stmt\Class_(null, ['type' => $self->semStack[$stackPos-(8-2)], 'extends' => $self->semStack[$stackPos-(8-4)], 'implements' => $self->semStack[$stackPos-(8-5)], 'stmts' => $self->semStack[$stackPos-(8-7)], 'attrGroups' => $self->semStack[$stackPos-(8-1)]], $self->getAttributes($self->tokenStartStack[$stackPos-(8-1)], $self->tokenEndStack[$stackPos])), $self->semStack[$stackPos-(8-3)]);
            $self->checkClass($self->semValue[0], -1);
            },
            504 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\New_($self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            505 => static function ($self, $stackPos) {
                 list($class, $ctorArgs) = $self->semStack[$stackPos-(2-2)]; $self->semValue = new Expr\New_($class, $ctorArgs, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            506 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\New_($self->semStack[$stackPos-(2-2)], [], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            507 => null,
            508 => null,
            509 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            510 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(4-3)];
            },
            511 => null,
            512 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            513 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            514 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ClosureUse($self->semStack[$stackPos-(2-2)], $self->semStack[$stackPos-(2-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            515 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            516 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            517 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            518 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\FuncCall($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            519 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            520 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            521 => null,
            522 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            523 => static function ($self, $stackPos) {
                 $self->semValue = new Name($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            524 => static function ($self, $stackPos) {
                 $self->semValue = new Name\FullyQualified(substr($self->semStack[$stackPos-(1-1)], 1), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            525 => static function ($self, $stackPos) {
                 $self->semValue = new Name\Relative(substr($self->semStack[$stackPos-(1-1)], 10), $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            526 => null,
            527 => null,
            528 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            529 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            530 => null,
            531 => null,
            532 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            533 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]); foreach ($self->semValue as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $self->phpVersion->supportsUnicodeEscapes()); } };
            },
            534 => static function ($self, $stackPos) {
                 foreach ($self->semStack[$stackPos-(1-1)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '`', $self->phpVersion->supportsUnicodeEscapes()); } }; $self->semValue = $self->semStack[$stackPos-(1-1)];
            },
            535 => static function ($self, $stackPos) {
                 $self->semValue = array();
            },
            536 => null,
            537 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ConstFetch($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            538 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Line($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            539 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\File($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            540 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Dir($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            541 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Class_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            542 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Trait_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            543 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Method($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            544 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Function_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            545 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Namespace_($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            546 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\MagicConst\Property($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            547 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            548 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(5-1)], $self->semStack[$stackPos-(5-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(5-1)], $self->tokenEndStack[$stackPos]));
            },
            549 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ClassConstFetch($self->semStack[$stackPos-(3-1)], new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)])), $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            550 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_SHORT;
            $self->semValue = new Expr\Array_($self->semStack[$stackPos-(3-2)], $attrs);
            },
            551 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Expr\Array_::KIND_LONG;
            $self->semValue = new Expr\Array_($self->semStack[$stackPos-(4-3)], $attrs);
            $self->createdArrays->attach($self->semValue);
            },
            552 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $self->createdArrays->attach($self->semValue);
            },
            553 => static function ($self, $stackPos) {
                 $self->semValue = Scalar\String_::fromString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]), $self->phpVersion->supportsUnicodeEscapes());
            },
            554 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]); $attrs['kind'] = Scalar\String_::KIND_DOUBLE_QUOTED;
            foreach ($self->semStack[$stackPos-(3-2)] as $s) { if ($s instanceof Node\InterpolatedStringPart) { $s->value = Node\Scalar\String_::parseEscapeSequences($s->value, '"', $self->phpVersion->supportsUnicodeEscapes()); } }; $self->semValue = new Scalar\InterpolatedString($self->semStack[$stackPos-(3-2)], $attrs);
            },
            555 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseLNumber($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]), $self->phpVersion->allowsInvalidOctals());
            },
            556 => static function ($self, $stackPos) {
                 $self->semValue = Scalar\Float_::fromString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            557 => null,
            558 => null,
            559 => null,
            560 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)]), true);
            },
            561 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(2-1)], '', $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(2-2)],  $self->tokenEndStack[$stackPos-(2-2)]), true);
            },
            562 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseDocString($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-2)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]), $self->getAttributes($self->tokenStartStack[$stackPos-(3-3)],  $self->tokenEndStack[$stackPos-(3-3)]), true);
            },
            563 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            564 => null,
            565 => null,
            566 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            567 => null,
            568 => null,
            569 => null,
            570 => null,
            571 => null,
            572 => null,
            573 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            574 => null,
            575 => null,
            576 => null,
            577 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            578 => null,
            579 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\MethodCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            580 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafeMethodCall($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->semStack[$stackPos-(4-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            581 => static function ($self, $stackPos) {
                 $self->semValue = null;
            },
            582 => null,
            583 => null,
            584 => null,
            585 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            586 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            587 => null,
            588 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            589 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            590 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable(new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos])), $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            591 => static function ($self, $stackPos) {
                 $var = $self->semStack[$stackPos-(1-1)]->name; $self->semValue = \is_string($var) ? new Node\VarLikeIdentifier($var, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])) : $var;
            },
            592 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            593 => null,
            594 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            595 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            596 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            597 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            598 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\StaticPropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            599 => null,
            600 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            601 => null,
            602 => null,
            603 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            604 => null,
            605 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Error($self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos])); $self->errorState = 2;
            },
            606 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\List_($self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos])); $self->semValue->setAttribute('kind', Expr\List_::KIND_LIST);
            $self->postprocessList($self->semValue);
            },
            607 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(1-1)]; $end = count($self->semValue)-1; if ($self->semValue[$end]->value instanceof Expr\Error) array_pop($self->semValue);
            },
            608 => null,
            609 => static function ($self, $stackPos) {
                 /* do nothing -- prevent default action of $$=$self->semStack[$1]. See $551. */
            },
            610 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(3-1)][] = $self->semStack[$stackPos-(3-3)]; $self->semValue = $self->semStack[$stackPos-(3-1)];
            },
            611 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            612 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(1-1)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            613 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(2-2)], null, true, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            614 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(1-1)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            615 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(3-3)], $self->semStack[$stackPos-(3-1)], false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            616 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(4-4)], $self->semStack[$stackPos-(4-1)], true, $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            617 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(3-3)], $self->semStack[$stackPos-(3-1)], false, $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            618 => static function ($self, $stackPos) {
                 $self->semValue = new Node\ArrayItem($self->semStack[$stackPos-(2-2)], null, false, $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]), true);
            },
            619 => static function ($self, $stackPos) {
                 /* Create an Error node now to remember the position. We'll later either report an error,
             or convert this into a null element, depending on whether this is a creation or destructuring context. */
          $attrs = $self->createEmptyElemAttributes($self->tokenPos);
          $self->semValue = new Node\ArrayItem(new Expr\Error($attrs), null, false, $attrs);
            },
            620 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            621 => static function ($self, $stackPos) {
                 $self->semStack[$stackPos-(2-1)][] = $self->semStack[$stackPos-(2-2)]; $self->semValue = $self->semStack[$stackPos-(2-1)];
            },
            622 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(1-1)]);
            },
            623 => static function ($self, $stackPos) {
                 $self->semValue = array($self->semStack[$stackPos-(2-1)], $self->semStack[$stackPos-(2-2)]);
            },
            624 => static function ($self, $stackPos) {
                 $attrs = $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]); $attrs['rawValue'] = $self->semStack[$stackPos-(1-1)]; $self->semValue = new Node\InterpolatedStringPart($self->semStack[$stackPos-(1-1)], $attrs);
            },
            625 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            626 => null,
            627 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(4-1)], $self->semStack[$stackPos-(4-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(4-1)], $self->tokenEndStack[$stackPos]));
            },
            628 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\PropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            629 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\NullsafePropertyFetch($self->semStack[$stackPos-(3-1)], $self->semStack[$stackPos-(3-3)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            630 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            631 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\Variable($self->semStack[$stackPos-(3-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(3-1)], $self->tokenEndStack[$stackPos]));
            },
            632 => static function ($self, $stackPos) {
                 $self->semValue = new Expr\ArrayDimFetch($self->semStack[$stackPos-(6-2)], $self->semStack[$stackPos-(6-4)], $self->getAttributes($self->tokenStartStack[$stackPos-(6-1)], $self->tokenEndStack[$stackPos]));
            },
            633 => static function ($self, $stackPos) {
                 $self->semValue = $self->semStack[$stackPos-(3-2)];
            },
            634 => static function ($self, $stackPos) {
                 $self->semValue = new Scalar\String_($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            635 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseNumString($self->semStack[$stackPos-(1-1)], $self->getAttributes($self->tokenStartStack[$stackPos-(1-1)], $self->tokenEndStack[$stackPos]));
            },
            636 => static function ($self, $stackPos) {
                 $self->semValue = $self->parseNumString('-' . $self->semStack[$stackPos-(2-2)], $self->getAttributes($self->tokenStartStack[$stackPos-(2-1)], $self->tokenEndStack[$stackPos]));
            },
            637 => null,
        ];
    }
}
