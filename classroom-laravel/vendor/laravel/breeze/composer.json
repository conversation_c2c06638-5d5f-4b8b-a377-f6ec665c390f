{"name": "laravel/breeze", "description": "Minimal Laravel authentication scaffolding with Blade and Tailwind.", "keywords": ["laravel", "auth"], "license": "MIT", "support": {"issues": "https://github.com/laravel/breeze/issues", "source": "https://github.com/laravel/breeze"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2.0", "illuminate/console": "^11.0|^12.0", "illuminate/filesystem": "^11.0|^12.0", "illuminate/support": "^11.0|^12.0", "illuminate/validation": "^11.0|^12.0", "symfony/console": "^7.0"}, "require-dev": {"laravel/framework": "^11.0|^12.0", "orchestra/testbench-core": "^9.0|^10.0", "phpstan/phpstan": "^2.0"}, "autoload": {"psr-4": {"Laravel\\Breeze\\": "src/"}}, "extra": {"laravel": {"providers": ["Laravel\\Breeze\\BreezeServiceProvider"]}}, "config": {"sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}