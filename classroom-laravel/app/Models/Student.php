<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Student extends Model
{
    protected $fillable = [
        'student_code',
        'full_name',
        'date_of_birth',
        'gender',
        'parent_name',
        'parent_phone',
        'address',
        'google_drive_link',
        'class_id',
        'status',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
    ];

    // Relationships
    public function class()
    {
        return $this->belongsTo(ClassRoom::class, 'class_id');
    }

    public function attendance()
    {
        return $this->hasMany(Attendance::class, 'student_id');
    }

    // Accessors
    public function getAgeAttribute()
    {
        return $this->date_of_birth ? $this->date_of_birth->age : null;
    }
}
