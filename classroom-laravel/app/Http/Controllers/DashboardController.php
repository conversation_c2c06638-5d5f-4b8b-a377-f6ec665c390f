<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Student;
use App\Models\ClassRoom;
use App\Models\Schedule;
use App\Models\Attendance;

class DashboardController extends Controller
{
    public function index()
    {
        // Statistics
        $totalStudents = Student::where('status', 'active')->count();
        $totalClasses = ClassRoom::where('status', 'active')->count();
        $totalTeachers = User::where('role', 'teacher')->where('status', 'active')->count();
        $todaySchedules = Schedule::whereDate('schedule_date', today())->where('status', 'scheduled')->count();

        // Today's absent students
        $todayAbsent = Attendance::whereHas('schedule', function($query) {
            $query->whereDate('schedule_date', today());
        })->where('status', 'absent')->distinct('student_id')->count();

        // Recent schedules
        $recentSchedules = Schedule::with(['class', 'teacher'])
            ->whereBetween('schedule_date', [now()->startOfWeek(), now()->endOfWeek()])
            ->where('status', 'scheduled')
            ->orderBy('schedule_date')
            ->orderBy('time_slot')
            ->limit(10)
            ->get();

        return view('dashboard', compact(
            'totalStudents',
            'totalClasses',
            'totalTeachers',
            'todaySchedules',
            'todayAbsent',
            'recentSchedules'
        ));
    }
}
