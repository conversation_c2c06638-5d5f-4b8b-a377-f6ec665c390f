[2025-06-25 13:48:05] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: mysql, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `username` var<PERSON><PERSON>(255) not null, `name` varcha<PERSON>(255) not null, `email` varchar(255) not null, `phone` varchar(255) null, `role` enum('admin', 'manager', 'teacher') not null default 'teacher', `status` enum('active', 'inactive') not null default 'active', `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists (Connection: mysql, SQL: create table `users` (`id` bigint unsigned not null auto_increment primary key, `username` varchar(255) not null, `name` varchar(255) not null, `email` varchar(255) not null, `phone` varchar(255) null, `role` enum('admin', 'manager', 'teacher') not null default 'teacher', `status` enum('active', 'inactive') not null default 'active', `email_verified_at` timestamp null, `password` varchar(255) not null, `remember_token` varchar(100) null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `u...')
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Volumes/MOXC/M...', 1, false)
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'users' already exists at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `u...', Array)
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `u...', Array, Object(Closure))
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('create table `u...', Array, Object(Closure))
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `u...')
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('users', Object(Closure))
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/database/migrations/0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('0001_01_01_0000...', Object(Closure))
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '0001_01_01_0000...', Object(Closure))
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Volumes/MOXC/M...', 1, false)
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-25 13:48:20] local.ERROR: SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'classes' (Connection: mysql, SQL: alter table `students` add constraint `students_class_id_foreign` foreign key (`class_id`) references `classes` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'classes' (Connection: mysql, SQL: alter table `students` add constraint `students_class_id_foreign` foreign key (`class_id`) references `classes` (`id`) on delete set null) at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `st...', Array, Object(Closure))
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `st...', Array, Object(Closure))
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `st...')
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('students', Object(Closure))
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/database/migrations/2025_06_25_134637_create_students_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_25_1346...', Object(Closure))
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_25_1346...', Object(Closure))
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Volumes/MOXC/M...', 1, false)
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1824 Failed to open the referenced table 'classes' at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `st...', Array)
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `st...', Array, Object(Closure))
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('alter table `st...', Array, Object(Closure))
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `st...')
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Schema/Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('students', Object(Closure))
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/database/migrations/2025_06_25_134637_create_students_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/View/Components/Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_25_1346...', Object(Closure))
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_25_1346...', Object(Closure))
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('/Volumes/MOXC/M...', 1, false)
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Console/Migrations/MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Command/Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Console/Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/symfony/console/Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-25 14:00:42] local.ERROR: Call to undefined method App\Models\Attendance::schedule() {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Attendance::schedule() at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php:67)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('schedule')
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'schedule', Array)
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(1108): Illuminate\\Database\\Eloquent\\Model->__call('schedule', Array)
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Relation.php(119): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\Concerns\\{closure}()
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(1107): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(46): Illuminate\\Database\\Eloquent\\Builder->getRelationWithoutConstraints('schedule')
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(163): Illuminate\\Database\\Eloquent\\Builder->has('schedule', '>=', 1, 'and', Object(Closure))
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->whereHas('schedule', Object(Closure))
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'whereHas', Array)
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('whereHas', Array)
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/app/Http/Controllers/DashboardController.php(23): Illuminate\\Database\\Eloquent\\Model::__callStatic('whereHas', Array)
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/MOXC/M...')
#64 {main}
"} 
[2025-06-25 14:02:33] local.ERROR: Call to undefined method App\Models\Attendance::schedule() {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Attendance::schedule() at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php:67)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('schedule')
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'schedule', Array)
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(1108): Illuminate\\Database\\Eloquent\\Model->__call('schedule', Array)
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Relation.php(119): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\Concerns\\{closure}()
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(1107): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(46): Illuminate\\Database\\Eloquent\\Builder->getRelationWithoutConstraints('schedule')
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(163): Illuminate\\Database\\Eloquent\\Builder->has('schedule', '>=', 1, 'and', Object(Closure))
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->whereHas('schedule', Object(Closure))
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'whereHas', Array)
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('whereHas', Array)
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/app/Http/Controllers/DashboardController.php(23): Illuminate\\Database\\Eloquent\\Model::__callStatic('whereHas', Array)
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/MOXC/M...')
#64 {main}
"} 
[2025-06-25 14:02:40] local.ERROR: Call to undefined method App\Models\Attendance::schedule() {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Attendance::schedule() at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php:67)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('schedule')
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'schedule', Array)
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(1108): Illuminate\\Database\\Eloquent\\Model->__call('schedule', Array)
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Relation.php(119): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\Concerns\\{closure}()
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(1107): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(46): Illuminate\\Database\\Eloquent\\Builder->getRelationWithoutConstraints('schedule')
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(163): Illuminate\\Database\\Eloquent\\Builder->has('schedule', '>=', 1, 'and', Object(Closure))
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->whereHas('schedule', Object(Closure))
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'whereHas', Array)
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('whereHas', Array)
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/app/Http/Controllers/DashboardController.php(23): Illuminate\\Database\\Eloquent\\Model::__callStatic('whereHas', Array)
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/MOXC/M...')
#64 {main}
"} 
[2025-06-25 14:03:09] local.ERROR: Call to undefined method App\Models\Attendance::schedule() {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Models\\Attendance::schedule() at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php:67)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(36): Illuminate\\Database\\Eloquent\\Model::throwBadMethodCallException('schedule')
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'schedule', Array)
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(1108): Illuminate\\Database\\Eloquent\\Model->__call('schedule', Array)
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Relations/Relation.php(119): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\Concerns\\{closure}()
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(1107): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(46): Illuminate\\Database\\Eloquent\\Builder->getRelationWithoutConstraints('schedule')
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/QueriesRelationships.php(163): Illuminate\\Database\\Eloquent\\Builder->has('schedule', '>=', 1, 'and', Object(Closure))
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Support/Traits/ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->whereHas('schedule', Object(Closure))
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'whereHas', Array)
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('whereHas', Array)
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/app/Http/Controllers/DashboardController.php(23): Illuminate\\Database\\Eloquent\\Model::__callStatic('whereHas', Array)
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#36 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#37 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#38 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#39 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/MOXC/M...')
#64 {main}
"} 
[2025-06-25 14:04:20] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'classroom_management.attendances' doesn't exist (Connection: mysql, SQL: select count(distinct `student_id`) as aggregate from `attendances` where exists (select * from `schedules` where `attendances`.`schedule_id` = `schedules`.`id` and date(`schedule_date`) = 2025-06-25) and `status` = absent) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'classroom_management.attendances' doesn't exist (Connection: mysql, SQL: select count(distinct `student_id`) as aggregate from `attendances` where exists (select * from `schedules` where `attendances`.`schedule_id` = `schedules`.`id` and date(`schedule_date`) = 2025-06-25) and `status` = absent) at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(di...', Array, Object(Closure))
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(di...', Array, Object(Closure))
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(di...', Array, true)
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/app/Http/Controllers/DashboardController.php(25): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/MOXC/M...')
#63 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'classroom_management.attendances' doesn't exist at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select count(di...')
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(di...', Array)
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(di...', Array, Object(Closure))
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(di...', Array, Object(Closure))
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(di...', Array, true)
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/app/Http/Controllers/DashboardController.php(25): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#64 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/MOXC/M...')
#65 {main}
"} 
[2025-06-25 14:09:43] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'classroom_management.attendances' doesn't exist (Connection: mysql, SQL: select count(distinct `student_id`) as aggregate from `attendances` where exists (select * from `schedules` where `attendances`.`schedule_id` = `schedules`.`id` and date(`schedule_date`) = 2025-06-25) and `status` = absent) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'classroom_management.attendances' doesn't exist (Connection: mysql, SQL: select count(distinct `student_id`) as aggregate from `attendances` where exists (select * from `schedules` where `attendances`.`schedule_id` = `schedules`.`id` and date(`schedule_date`) = 2025-06-25) and `status` = absent) at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(di...', Array, Object(Closure))
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(di...', Array, Object(Closure))
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(di...', Array, true)
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/app/Http/Controllers/DashboardController.php(25): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#36 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#37 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#38 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#62 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/MOXC/M...')
#63 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'classroom_management.attendances' doesn't exist at /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select count(di...')
#1 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select count(di...', Array)
#2 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(di...', Array, Object(Closure))
#3 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(di...', Array, Object(Closure))
#4 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(di...', Array, true)
#5 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3630): Illuminate\\Database\\Query\\Builder->get(Array)
#9 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3558): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#10 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->count()
#11 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/app/Http/Controllers/DashboardController.php(25): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#12 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): App\\Http\\Controllers\\DashboardController->index()
#13 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DashboardController), 'index')
#14 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#15 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#16 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/EnsureEmailIsVerified.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#61 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#62 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#63 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#64 /Volumes/MOXC/MOXC/PycharmProjects/FlaskProject/classroom-laravel/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/MOXC/M...')
#65 {main}
"} 
