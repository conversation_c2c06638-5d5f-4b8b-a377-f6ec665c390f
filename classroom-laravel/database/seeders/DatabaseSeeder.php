<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        \App\Models\User::create([
            'username' => 'admin',
            'name' => 'Administrator',
            'email' => '<EMAIL>',
            'role' => 'admin',
            'password' => bcrypt('admin123'),
        ]);

        // Create manager user
        \App\Models\User::create([
            'username' => 'manager',
            'name' => 'Quản sinh',
            'email' => '<EMAIL>',
            'role' => 'manager',
            'password' => bcrypt('manager123'),
        ]);

        // Create teacher user
        $teacher = \App\Models\User::create([
            'username' => 'teacher',
            'name' => '<PERSON><PERSON><PERSON><PERSON> viê<PERSON>',
            'email' => '<EMAIL>',
            'role' => 'teacher',
            'password' => bcrypt('teacher123'),
        ]);

        // Create classes
        $class1 = \App\Models\ClassRoom::create([
            'class_name' => 'Lớp 10A1',
            'class_code' => '10A1',
            'description' => 'Lớp 10 chuyên Toán',
            'teacher_id' => $teacher->id,
        ]);

        $class2 = \App\Models\ClassRoom::create([
            'class_name' => 'Lớp 11B2',
            'class_code' => '11B2',
            'description' => 'Lớp 11 chuyên Lý',
            'teacher_id' => $teacher->id,
        ]);

        // Create students
        \App\Models\Student::create([
            'student_code' => 'HS20250001',
            'full_name' => 'Nguyễn Văn An',
            'date_of_birth' => '2008-05-15',
            'gender' => 'male',
            'parent_name' => 'Nguyễn Văn Bình',
            'parent_phone' => '0901234567',
            'class_id' => $class1->id,
        ]);

        \App\Models\Student::create([
            'student_code' => 'HS20250002',
            'full_name' => 'Trần Thị Bảo',
            'date_of_birth' => '2008-08-20',
            'gender' => 'female',
            'parent_name' => 'Trần Văn Cường',
            'parent_phone' => '0907654321',
            'class_id' => $class1->id,
        ]);

        \App\Models\Student::create([
            'student_code' => 'HS20250003',
            'full_name' => 'Lê Minh Đức',
            'date_of_birth' => '2007-12-10',
            'gender' => 'male',
            'parent_name' => 'Lê Văn Em',
            'parent_phone' => '0912345678',
            'class_id' => $class2->id,
        ]);
    }
}
