<x-guest-layout>
    <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-400 via-amber-500 to-yellow-500 py-12 px-4 sm:px-6 lg:px-8">

        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-20 w-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                    <span class="text-4xl">🏫</span>
                </div>
                <h2 class="text-4xl font-bold text-white mb-2">
                    <PERSON><PERSON> thống Quản lý Lớp học
                </h2>
                <p class="text-orange-100 text-lg">
                    Đăng nhập để tiếp tục
                </p>
            </div>

            <!-- Login Card -->
            <div class="bg-white bg-opacity-95 rounded-2xl shadow-2xl p-8">
                <!-- Session Status -->
                <x-auth-session-status class="mb-4" :status="session('status')" />

                <form method="POST" action="{{ route('login') }}" class="space-y-6">
                    @csrf

                    <!-- Email Address -->
                    <div class="space-y-2">
                        <x-input-label for="email" :value="__('📧 Email / Tên đăng nhập')" class="text-gray-700 font-semibold" />
                        <x-text-input id="email"
                            class="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl focus:ring-orange-500 focus:border-orange-500 bg-orange-50"
                            type="email"
                            name="email"
                            :value="old('email')"
                            required
                            autofocus
                            autocomplete="username"
                            placeholder="Nhập email hoặc tên đăng nhập" />
                        <x-input-error :messages="$errors->get('email')" class="mt-2" />
                    </div>

                    <!-- Password -->
                    <div class="space-y-2">
                        <x-input-label for="password" :value="__('🔒 Mật khẩu')" class="text-gray-700 font-semibold" />
                        <x-text-input id="password"
                            class="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl focus:ring-orange-500 focus:border-orange-500 bg-orange-50"
                            type="password"
                            name="password"
                            required
                            autocomplete="current-password"
                            placeholder="Nhập mật khẩu" />
                        <x-input-error :messages="$errors->get('password')" class="mt-2" />
                    </div>

                    <!-- Remember Me -->
                    <div class="flex items-center justify-between">
                        <label for="remember_me" class="flex items-center">
                            <input id="remember_me" type="checkbox" class="rounded border-orange-300 text-orange-600 shadow-sm focus:ring-orange-500 w-4 h-4" name="remember">
                            <span class="ml-2 text-sm text-gray-600 font-medium">Ghi nhớ đăng nhập</span>
                        </label>

                        @if (Route::has('password.request'))
                            <a class="text-sm text-orange-600 hover:text-orange-800 font-medium transition-colors duration-200" href="{{ route('password.request') }}">
                                Quên mật khẩu?
                            </a>
                        @endif
                    </div>

                    <!-- Login Button -->
                    <div class="space-y-4">
                        <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                            🚀 Đăng nhập
                        </button>

                        <!-- Demo Accounts -->
                        <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                            <h4 class="text-sm font-semibold text-orange-800 mb-2">🔑 Tài khoản demo:</h4>
                            <div class="text-xs text-orange-700 space-y-1">
                                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                                <p><strong>Quản sinh:</strong> <EMAIL> / manager123</p>
                                <p><strong>Giáo viên:</strong> <EMAIL> / teacher123</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Footer -->
            <div class="text-center">
                <p class="text-orange-100 text-sm">
                    © {{ date('Y') }} Hệ thống Quản lý Lớp học. Phát triển bởi <strong>Augment Code</strong>
                </p>
            </div>
        </div>
    </div>


</x-guest-layout>
