<x-guest-layout>
    <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-400 via-amber-500 to-yellow-500 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-20 w-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-4 animate-bounce">
                    <span class="text-4xl">🏫</span>
                </div>
                <h2 class="text-4xl font-bold text-white mb-2">
                    Đ<PERSON>ng ký tài khoản
                </h2>
                <p class="text-orange-100 text-lg">
                    Tạo tài khoản mới để sử dụng hệ thống
                </p>
            </div>

            <!-- Register Card -->
            <div class="bg-white bg-opacity-95 backdrop-blur-lg rounded-2xl shadow-2xl p-8">
                <form method="POST" action="{{ route('register') }}" class="space-y-6">
                    @csrf

                    <!-- Name -->
                    <div class="space-y-2">
                        <x-input-label for="name" :value="__('👤 Họ tên')" class="text-gray-700 font-semibold" />
                        <x-text-input id="name"
                            class="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-orange-50 hover:bg-white"
                            type="text"
                            name="name"
                            :value="old('name')"
                            required
                            autofocus
                            autocomplete="name"
                            placeholder="Nhập họ tên đầy đủ" />
                        <x-input-error :messages="$errors->get('name')" class="mt-2" />
                    </div>

                    <!-- Email Address -->
                    <div class="space-y-2">
                        <x-input-label for="email" :value="__('📧 Email')" class="text-gray-700 font-semibold" />
                        <x-text-input id="email"
                            class="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-orange-50 hover:bg-white"
                            type="email"
                            name="email"
                            :value="old('email')"
                            required
                            autocomplete="username"
                            placeholder="Nhập địa chỉ email" />
                        <x-input-error :messages="$errors->get('email')" class="mt-2" />
                    </div>

                    <!-- Password -->
                    <div class="space-y-2">
                        <x-input-label for="password" :value="__('🔒 Mật khẩu')" class="text-gray-700 font-semibold" />
                        <x-text-input id="password"
                            class="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-orange-50 hover:bg-white"
                            type="password"
                            name="password"
                            required
                            autocomplete="new-password"
                            placeholder="Nhập mật khẩu" />
                        <x-input-error :messages="$errors->get('password')" class="mt-2" />
                    </div>

                    <!-- Confirm Password -->
                    <div class="space-y-2">
                        <x-input-label for="password_confirmation" :value="__('🔒 Xác nhận mật khẩu')" class="text-gray-700 font-semibold" />
                        <x-text-input id="password_confirmation"
                            class="block w-full px-4 py-3 border-2 border-orange-200 rounded-xl focus:ring-orange-500 focus:border-orange-500 transition-all duration-200 bg-orange-50 hover:bg-white"
                            type="password"
                            name="password_confirmation"
                            required
                            autocomplete="new-password"
                            placeholder="Nhập lại mật khẩu" />
                        <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
                    </div>

                    <div class="space-y-4">
                        <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                            🚀 Đăng ký
                        </button>

                        <div class="text-center">
                            <a class="text-sm text-orange-600 hover:text-orange-800 font-medium transition-colors duration-200" href="{{ route('login') }}">
                                Đã có tài khoản? Đăng nhập ngay
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-guest-layout>
